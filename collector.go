package binance

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2"
	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/models"
)

// DataCollector 表示数据采集器
type DataCollector struct {
	client       *Client
	log          *logrus.Logger
	storage      DataStorage
	symbols      []string
	intervals    []string
	stopChans    map[string]chan struct{}
	wg           sync.WaitGroup
	ctx          context.Context
	cancel       context.CancelFunc
	bufferSize   int
	collectDepth bool
	collectTrade bool
}

// DataStorage 表示数据存储接口
type DataStorage interface {
	SaveKLineData(data []models.KLineData) error
	SaveTradeData(data []models.TradeData) error
	SaveOrderBookData(data models.OrderBookData) error
	SaveTickerData(data models.TickerData) error
}

// CollectorConfig 表示数据采集器配置
type CollectorConfig struct {
	Client       *Client
	Log          *logrus.Logger
	Storage      DataStorage
	Symbols      []string
	Intervals    []string
	BufferSize   int
	CollectDepth bool
	CollectTrade bool
}

// NewDataCollector 创建一个新的数据采集器
func NewDataCollector(cfg CollectorConfig) *DataCollector {
	ctx, cancel := context.WithCancel(context.Background())

	return &DataCollector{
		client:       cfg.Client,
		log:          cfg.Log,
		storage:      cfg.Storage,
		symbols:      cfg.Symbols,
		intervals:    cfg.Intervals,
		stopChans:    make(map[string]chan struct{}),
		ctx:          ctx,
		cancel:       cancel,
		bufferSize:   cfg.BufferSize,
		collectDepth: cfg.CollectDepth,
		collectTrade: cfg.CollectTrade,
	}
}

// Start 启动数据采集
func (c *DataCollector) Start() error {
	c.log.Info("启动数据采集器")

	// 启动K线数据采集
	for _, symbol := range c.symbols {
		for _, interval := range c.intervals {
			if err := c.startKlineCollection(symbol, interval); err != nil {
				return fmt.Errorf("启动K线数据采集失败: %w", err)
			}
		}

		// 启动订单簿数据采集
		if c.collectDepth {
			if err := c.startDepthCollection(symbol); err != nil {
				return fmt.Errorf("启动订单簿数据采集失败: %w", err)
			}
		}

		// 启动交易数据采集
		if c.collectTrade {
			if err := c.startTradeCollection(symbol); err != nil {
				return fmt.Errorf("启动交易数据采集失败: %w", err)
			}
		}
	}

	return nil
}

// Stop 停止数据采集
func (c *DataCollector) Stop() {
	c.log.Info("停止数据采集器")

	// 取消上下文
	c.cancel()

	// 关闭所有停止通道
	for _, stopC := range c.stopChans {
		close(stopC)
	}

	// 等待所有goroutine退出
	c.wg.Wait()
}

// startKlineCollection 启动K线数据采集
func (c *DataCollector) startKlineCollection(symbol, interval string) error {
	c.log.WithFields(logrus.Fields{
		"symbol":   symbol,
		"interval": interval,
	}).Info("启动K线数据采集")

	// 创建一个缓冲通道来存储K线数据
	klineC := make(chan *binance.WsKlineEvent, c.bufferSize)

	// 创建一个处理函数来处理K线事件
	wsKlineHandler := func(event *binance.WsKlineEvent) {
		select {
		case klineC <- event:
		default:
			c.log.Warn("K线数据缓冲区已满，丢弃数据")
		}
	}

	// 创建一个错误处理函数
	errHandler := func(err error) {
		c.log.WithError(err).Error("K线WebSocket错误")
	}

	// 订阅K线数据
	doneC, stopC, err := binance.WsKlineServe(symbol, interval, wsKlineHandler, errHandler)
	if err != nil {
		return fmt.Errorf("订阅K线数据失败: %w", err)
	}

	// 保存停止通道
	key := fmt.Sprintf("kline_%s_%s", symbol, interval)
	c.stopChans[key] = stopC

	// 启动一个goroutine来处理K线数据
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()

		// 创建一个缓冲区来批量保存数据
		buffer := make([]models.KLineData, 0, 100)
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-c.ctx.Done():
				return
			case <-doneC:
				return
			case event := <-klineC:
				// 转换为KLineData
				kline := models.KLineData{
					Symbol:       event.Symbol,
					Interval:     event.Kline.Interval,
					OpenTime:     time.Unix(0, event.Kline.StartTime*int64(time.Millisecond)),
					CloseTime:    time.Unix(0, event.Kline.EndTime*int64(time.Millisecond)),
					Open:         parseFloat(event.Kline.Open),
					High:         parseFloat(event.Kline.High),
					Low:          parseFloat(event.Kline.Low),
					Close:        parseFloat(event.Kline.Close),
					Volume:       parseFloat(event.Kline.Volume),
					TradeCount:   event.Kline.TradeNum,
					QuoteVolume:  parseFloat(event.Kline.QuoteVolume),
					TakerBuyVol:  parseFloat(event.Kline.TakerBuyBaseVolume),
					TakerBuyQuoteVol: parseFloat(event.Kline.TakerBuyQuoteVolume),
				}

				// 添加到缓冲区
				buffer = append(buffer, kline)

				// 如果缓冲区已满，保存数据
				if len(buffer) >= 100 {
					if err := c.storage.SaveKLineData(buffer); err != nil {
						c.log.WithError(err).Error("保存K线数据失败")
					}
					buffer = buffer[:0]
				}
			case <-ticker.C:
				// 定期保存缓冲区中的数据
				if len(buffer) > 0 {
					if err := c.storage.SaveKLineData(buffer); err != nil {
						c.log.WithError(err).Error("保存K线数据失败")
					}
					buffer = buffer[:0]
				}
			}
		}
	}()

	return nil
}

// startDepthCollection 启动订单簿数据采集
func (c *DataCollector) startDepthCollection(symbol string) error {
	c.log.WithField("symbol", symbol).Info("启动订单簿数据采集")

	// 创建一个缓冲通道来存储订单簿数据
	depthC := make(chan *binance.WsDepthEvent, c.bufferSize)

	// 创建一个处理函数来处理订单簿事件
	wsDepthHandler := func(event *binance.WsDepthEvent) {
		select {
		case depthC <- event:
		default:
			c.log.Warn("订单簿数据缓冲区已满，丢弃数据")
		}
	}

	// 创建一个错误处理函数
	errHandler := func(err error) {
		c.log.WithError(err).Error("订单簿WebSocket错误")
	}

	// 订阅订单簿数据
	doneC, stopC, err := binance.WsDepthServe(symbol, wsDepthHandler, errHandler)
	if err != nil {
		return fmt.Errorf("订阅订单簿数据失败: %w", err)
	}

	// 保存停止通道
	key := fmt.Sprintf("depth_%s", symbol)
	c.stopChans[key] = stopC

	// 启动一个goroutine来处理订单簿数据
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()

		for {
			select {
			case <-c.ctx.Done():
				return
			case <-doneC:
				return
			case event := <-depthC:
				// 转换为OrderBookData
				orderBook := models.OrderBookData{
					Symbol:       event.Symbol,
					Time:         time.Unix(0, event.Time*int64(time.Millisecond)),
					LastUpdateID: event.LastUpdateID,
					Bids:         make([]models.OrderBookEntry, len(event.Bids)),
					Asks:         make([]models.OrderBookEntry, len(event.Asks)),
				}

				// 转换买单
				for i, bid := range event.Bids {
					orderBook.Bids[i] = models.OrderBookEntry{
						Price:    parseFloat(bid.Price),
						Quantity: parseFloat(bid.Quantity),
					}
				}

				// 转换卖单
				for i, ask := range event.Asks {
					orderBook.Asks[i] = models.OrderBookEntry{
						Price:    parseFloat(ask.Price),
						Quantity: parseFloat(ask.Quantity),
					}
				}

				// 将买单和卖单转换为JSON字符串
				bidsJSON, err := json.Marshal(orderBook.Bids)
				if err != nil {
					c.log.WithError(err).Error("买单JSON序列化失败")
					continue
				}
				orderBook.BidsJSON = string(bidsJSON)

				asksJSON, err := json.Marshal(orderBook.Asks)
				if err != nil {
					c.log.WithError(err).Error("卖单JSON序列化失败")
					continue
				}
				orderBook.AsksJSON = string(asksJSON)

				// 保存订单簿数据
				if err := c.storage.SaveOrderBookData(orderBook); err != nil {
					c.log.WithError(err).Error("保存订单簿数据失败")
				}
			}
		}
	}()

	return nil
}

// startTradeCollection 启动交易数据采集
func (c *DataCollector) startTradeCollection(symbol string) error {
	c.log.WithField("symbol", symbol).Info("启动交易数据采集")

	// 创建一个缓冲通道来存储交易数据
	tradeC := make(chan *binance.WsTradeEvent, c.bufferSize)

	// 创建一个处理函数来处理交易事件
	wsTradeHandler := func(event *binance.WsTradeEvent) {
		select {
		case tradeC <- event:
		default:
			c.log.Warn("交易数据缓冲区已满，丢弃数据")
		}
	}

	// 创建一个错误处理函数
	errHandler := func(err error) {
		c.log.WithError(err).Error("交易WebSocket错误")
	}

	// 订阅交易数据
	doneC, stopC, err := binance.WsTradeServe(symbol, wsTradeHandler, errHandler)
	if err != nil {
		return fmt.Errorf("订阅交易数据失败: %w", err)
	}

	// 保存停止通道
	key := fmt.Sprintf("trade_%s", symbol)
	c.stopChans[key] = stopC

	// 启动一个goroutine来处理交易数据
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()

		// 创建一个缓冲区来批量保存数据
		buffer := make([]models.TradeData, 0, 100)
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-c.ctx.Done():
				return
			case <-doneC:
				return
			case event := <-tradeC:
				// 转换为TradeData
				trade := models.TradeData{
					Symbol:        event.Symbol,
					TradeID:       event.TradeID,
					Price:         parseFloat(event.Price),
					Quantity:      parseFloat(event.Quantity),
					QuoteQuantity: parseFloat(event.Price) * parseFloat(event.Quantity),
					Time:          time.Unix(0, event.Time*int64(time.Millisecond)),
					IsBuyerMaker:  event.IsBuyerMaker,
					IsBestMatch:   event.IsBestMatch,
				}

				// 添加到缓冲区
				buffer = append(buffer, trade)

				// 如果缓冲区已满，保存数据
				if len(buffer) >= 100 {
					if err := c.storage.SaveTradeData(buffer); err != nil {
						c.log.WithError(err).Error("保存交易数据失败")
					}
					buffer = buffer[:0]
				}
			case <-ticker.C:
				// 定期保存缓冲区中的数据
				if len(buffer) > 0 {
					if err := c.storage.SaveTradeData(buffer); err != nil {
						c.log.WithError(err).Error("保存交易数据失败")
					}
					buffer = buffer[:0]
				}
			}
		}
	}()

	return nil
}

// FetchHistoricalKlines 获取历史K线数据
func (c *DataCollector) FetchHistoricalKlines(symbol, interval string, startTime, endTime time.Time) error {
	c.log.WithFields(logrus.Fields{
		"symbol":    symbol,
		"interval":  interval,
		"startTime": startTime,
		"endTime":   endTime,
	}).Info("获取历史K线数据")

	// 设置最大限制
	limit := 1000

	// 计算时间间隔
	var timeStep time.Duration
	switch interval {
	case "1m":
		timeStep = 1000 * time.Minute
	case "3m":
		timeStep = 3000 * time.Minute
	case "5m":
		timeStep = 5000 * time.Minute
	case "15m":
		timeStep = 15000 * time.Minute
	case "30m":
		timeStep = 30000 * time.Minute
	case "1h":
		timeStep = 1000 * time.Hour
	case "2h":
		timeStep = 2000 * time.Hour
	case "4h":
		timeStep = 4000 * time.Hour
	case "6h":
		timeStep = 6000 * time.Hour
	case "8h":
		timeStep = 8000 * time.Hour
	case "12h":
		timeStep = 12000 * time.Hour
	case "1d":
		timeStep = 1000 * 24 * time.Hour
	case "3d":
		timeStep = 3000 * 24 * time.Hour
	case "1w":
		timeStep = 7000 * 24 * time.Hour
	case "1M":
		timeStep = 30000 * 24 * time.Hour
	default:
		timeStep = 1000 * time.Minute
	}

	// 分批获取数据
	current := startTime
	for current.Before(endTime) {
		// 计算批次结束时间
		batchEnd := current.Add(time.Duration(limit) * timeStep)
		if batchEnd.After(endTime) {
			batchEnd = endTime
		}

		// 获取K线数据
		klines, err := c.client.GetKlines(symbol, interval, current, batchEnd, limit)
		if err != nil {
			return fmt.Errorf("获取历史K线数据失败: %w", err)
		}

		// 转换为KLineData
		klineData := make([]models.KLineData, len(klines))
		for i, kline := range klines {
			openTime := time.Unix(0, kline.OpenTime*int64(time.Millisecond))
			closeTime := time.Unix(0, kline.CloseTime*int64(time.Millisecond))

			klineData[i] = models.KLineData{
				Symbol:       symbol,
				Interval:     interval,
				OpenTime:     openTime,
				CloseTime:    closeTime,
				Open:         parseFloat(kline.Open),
				High:         parseFloat(kline.High),
				Low:          parseFloat(kline.Low),
				Close:        parseFloat(kline.Close),
				Volume:       parseFloat(kline.Volume),
				TradeCount:   kline.TradeNum,
				QuoteVolume:  parseFloat(kline.QuoteVolume),
				TakerBuyVol:  parseFloat(kline.TakerBuyBaseVolume),
				TakerBuyQuoteVol: parseFloat(kline.TakerBuyQuoteVolume),
			}
		}

		// 保存K线数据
		if err := c.storage.SaveKLineData(klineData); err != nil {
			return fmt.Errorf("保存历史K线数据失败: %w", err)
		}

		// 更新当前时间
		if len(klines) > 0 {
			current = time.Unix(0, klines[len(klines)-1].CloseTime*int64(time.Millisecond))
		} else {
			break
		}

		// 添加一些延迟以避免API限制
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

// parseFloat 将字符串转换为浮点数
func parseFloat(s string) float64 {
	var f float64
	fmt.Sscanf(s, "%f", &f)
	return f
}

