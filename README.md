# BTC-Predictor

币安事件合约预判系统 - 使用Golang开发的比特币短期走势预测工具

## 项目概述

BTC-Predictor是一个基于Golang开发的币安事件合约预判程序，通过分析历史数据、趋势、舆情和流量等综合因素，预测比特币10分钟后的走势，目标准确率达到80%以上，实现稳定盈利。

## 核心功能

- 实时数据采集（价格、交易量、订单簿等）
- 历史数据分析和特征工程
- 舆情和流量数据分析
- 多模型预测（技术分析、机器学习、舆情分析）
- 综合预测算法（提高准确率）
- 风险管理和交易执行
- 回测和性能评估

## 系统架构

系统采用微服务架构，主要组件包括：

1. 数据采集引擎
2. 数据处理引擎
3. 预测算法引擎
4. 交易执行引擎
5. 回测系统

## 安装与使用

### 前置条件

- Go 1.20+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. 克隆仓库
```bash
git clone https://github.com/user/btc-predictor.git
cd btc-predictor
```

2. 安装依赖
```bash
go mod download
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，填入必要的配置信息
```

4. 构建项目
```bash
go build -o bin/btc-predictor cmd/main.go
```

5. 运行项目
```bash
./bin/btc-predictor
```

## 配置说明

主要配置文件位于`config`目录下，包括：

- `config.yaml`: 主配置文件
- `binance.yaml`: 币安API配置
- `models.yaml`: 预测模型配置
- `trading.yaml`: 交易策略配置

## 使用示例

### 启动预测服务

```bash
./bin/btc-predictor predict --symbol BTCUSDT --interval 10m
```

### 运行回测

```bash
./bin/btc-predictor backtest --symbol BTCUSDT --start 2023-01-01 --end 2023-06-01
```

### 启动交易服务

```bash
./bin/btc-predictor trade --symbol BTCUSDT --strategy default
```

## 许可证

MIT

