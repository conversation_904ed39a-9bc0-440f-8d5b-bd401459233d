package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/models"
	"github.com/user/btc-predictor/internal/storage"
)

// VisualizationHandler 表示可视化处理器
type VisualizationHandler struct {
	log     *logrus.Logger
	storage storage.Storage
}

// NewVisualizationHandler 创建一个新的可视化处理器
func NewVisualizationHandler(log *logrus.Logger, storage storage.Storage) *VisualizationHandler {
	return &VisualizationHandler{
		log:     log,
		storage: storage,
	}
}

// RegisterRoutes 注册路由
func (h *VisualizationHandler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/api/v1/visualization/price-chart/{symbol}", h.GetPriceChartData).Methods("GET")
	router.HandleFunc("/api/v1/visualization/prediction-chart/{symbol}", h.GetPredictionChartData).Methods("GET")
	router.HandleFunc("/api/v1/visualization/accuracy-chart/{symbol}", h.GetAccuracyChartData).Methods("GET")
	router.HandleFunc("/api/v1/visualization/performance-chart/{symbol}", h.GetPerformanceChartData).Methods("GET")
	router.HandleFunc("/api/v1/visualization/sentiment-chart", h.GetSentimentChartData).Methods("GET")
	router.HandleFunc("/api/v1/visualization/traffic-chart", h.GetTrafficChartData).Methods("GET")
	router.HandleFunc("/api/v1/visualization/dashboard", h.GetDashboardData).Methods("GET")
}

// PriceChartData 表示价格图表数据
type PriceChartData struct {
	Labels    []string  `json:"labels"`
	Prices    []float64 `json:"prices"`
	Volumes   []float64 `json:"volumes"`
	Timestamp time.Time `json:"timestamp"`
}

// PredictionChartData 表示预测图表数据
type PredictionChartData struct {
	Labels           []string  `json:"labels"`
	ActualPrices     []float64 `json:"actual_prices"`
	PredictedPrices  []float64 `json:"predicted_prices"`
	Confidences      []float64 `json:"confidences"`
	Directions       []string  `json:"directions"`
	PredictionTimes  []string  `json:"prediction_times"`
	EvaluationTimes  []string  `json:"evaluation_times"`
	Timestamp        time.Time `json:"timestamp"`
}

// AccuracyChartData 表示准确率图表数据
type AccuracyChartData struct {
	Labels           []string  `json:"labels"`
	Accuracies       []float64 `json:"accuracies"`
	UpAccuracies     []float64 `json:"up_accuracies"`
	DownAccuracies   []float64 `json:"down_accuracies"`
	NeutralAccuracies []float64 `json:"neutral_accuracies"`
	PredictionCounts []int     `json:"prediction_counts"`
	Timestamp        time.Time `json:"timestamp"`
}

// PerformanceChartData 表示性能图表数据
type PerformanceChartData struct {
	Labels          []string  `json:"labels"`
	Returns         []float64 `json:"returns"`
	CumulativeReturns []float64 `json:"cumulative_returns"`
	Drawdowns       []float64 `json:"drawdowns"`
	SharpeRatios    []float64 `json:"sharpe_ratios"`
	Timestamp       time.Time `json:"timestamp"`
}

// SentimentChartData 表示舆情图表数据
type SentimentChartData struct {
	Labels           []string  `json:"labels"`
	SentimentScores  []float64 `json:"sentiment_scores"`
	PositiveRatios   []float64 `json:"positive_ratios"`
	NegativeRatios   []float64 `json:"negative_ratios"`
	NeutralRatios    []float64 `json:"neutral_ratios"`
	InfluencerScores []float64 `json:"influencer_scores"`
	Timestamp        time.Time `json:"timestamp"`
}

// TrafficChartData 表示流量图表数据
type TrafficChartData struct {
	Labels              []string  `json:"labels"`
	TrafficScores       []float64 `json:"traffic_scores"`
	VisitorCounts       []int     `json:"visitor_counts"`
	SearchVolumes       []int     `json:"search_volumes"`
	ReferralCounts      []int     `json:"referral_counts"`
	Timestamp           time.Time `json:"timestamp"`
}

// DashboardData 表示仪表板数据
type DashboardData struct {
	LatestPrediction    *models.Prediction     `json:"latest_prediction"`
	AccuracyStats       *models.AccuracyStats  `json:"accuracy_stats"`
	PerformanceStats    *models.PerformanceStats `json:"performance_stats"`
	RecentPredictions   []models.Prediction    `json:"recent_predictions"`
	LatestSentiment     *models.SentimentAnalysis `json:"latest_sentiment"`
	LatestTraffic       *models.TrafficAnalysis `json:"latest_traffic"`
	Timestamp           time.Time              `json:"timestamp"`
}

// GetPriceChartData 获取价格图表数据
func (h *VisualizationHandler) GetPriceChartData(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	symbol := vars["symbol"]
	
	// 获取查询参数
	interval := r.URL.Query().Get("interval")
	if interval == "" {
		interval = "1m"
	}
	
	limitStr := r.URL.Query().Get("limit")
	limit := 100
	if limitStr != "" {
		var err error
		limit, err = strconv.Atoi(limitStr)
		if err != nil {
			http.Error(w, "Invalid limit parameter", http.StatusBadRequest)
			return
		}
	}
	
	// 获取K线数据
	klines, err := h.storage.GetRecentKLines(symbol, interval, limit)
	if err != nil {
		h.log.WithError(err).Error("获取K线数据失败")
		http.Error(w, "Failed to get price data", http.StatusInternalServerError)
		return
	}
	
	// 构建图表数据
	data := PriceChartData{
		Labels:    make([]string, len(klines)),
		Prices:    make([]float64, len(klines)),
		Volumes:   make([]float64, len(klines)),
		Timestamp: time.Now(),
	}
	
	for i, kline := range klines {
		data.Labels[i] = kline.OpenTime.Format("2006-01-02 15:04:05")
		data.Prices[i] = kline.Close
		data.Volumes[i] = kline.Volume
	}
	
	// 返回JSON响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

// GetPredictionChartData 获取预测图表数据
func (h *VisualizationHandler) GetPredictionChartData(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	symbol := vars["symbol"]
	
	// 获取查询参数
	limitStr := r.URL.Query().Get("limit")
	limit := 50
	if limitStr != "" {
		var err error
		limit, err = strconv.Atoi(limitStr)
		if err != nil {
			http.Error(w, "Invalid limit parameter", http.StatusBadRequest)
			return
		}
	}
	
	// 获取预测数据
	predictions, err := h.storage.GetPredictions(symbol, limit, 0)
	if err != nil {
		h.log.WithError(err).Error("获取预测数据失败")
		http.Error(w, "Failed to get prediction data", http.StatusInternalServerError)
		return
	}
	
	// 构建图表数据
	data := PredictionChartData{
		Labels:           make([]string, len(predictions)),
		ActualPrices:     make([]float64, len(predictions)),
		PredictedPrices:  make([]float64, len(predictions)),
		Confidences:      make([]float64, len(predictions)),
		Directions:       make([]string, len(predictions)),
		PredictionTimes:  make([]string, len(predictions)),
		EvaluationTimes:  make([]string, len(predictions)),
		Timestamp:        time.Now(),
	}
	
	for i, pred := range predictions {
		data.Labels[i] = pred.CreatedAt.Format("2006-01-02 15:04:05")
		data.ActualPrices[i] = pred.ActualPrice
		
		// 计算预测价格
		predictedPrice := pred.CurrentPrice * (1 + pred.PredictedChange/100)
		data.PredictedPrices[i] = predictedPrice
		
		data.Confidences[i] = pred.Confidence
		data.Directions[i] = pred.Direction
		data.PredictionTimes[i] = pred.CreatedAt.Format("2006-01-02 15:04:05")
		
		if !pred.EvaluatedAt.IsZero() {
			data.EvaluationTimes[i] = pred.EvaluatedAt.Format("2006-01-02 15:04:05")
		}
	}
	
	// 返回JSON响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

// GetAccuracyChartData 获取准确率图表数据
func (h *VisualizationHandler) GetAccuracyChartData(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	symbol := vars["symbol"]
	
	// 获取查询参数
	daysStr := r.URL.Query().Get("days")
	days := 30
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil {
			http.Error(w, "Invalid days parameter", http.StatusBadRequest)
			return
		}
	}
	
	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)
	
	// 获取准确率统计
	stats, err := h.storage.GetAccuracyStats(symbol, startTime, endTime)
	if err != nil {
		h.log.WithError(err).Error("获取准确率统计失败")
		http.Error(w, "Failed to get accuracy stats", http.StatusInternalServerError)
		return
	}
	
	// 构建图表数据
	data := AccuracyChartData{
		Labels:           make([]string, len(stats.DailyStats)),
		Accuracies:       make([]float64, len(stats.DailyStats)),
		UpAccuracies:     make([]float64, len(stats.DailyStats)),
		DownAccuracies:   make([]float64, len(stats.DailyStats)),
		NeutralAccuracies: make([]float64, len(stats.DailyStats)),
		PredictionCounts: make([]int, len(stats.DailyStats)),
		Timestamp:        time.Now(),
	}
	
	for i, daily := range stats.DailyStats {
		data.Labels[i] = daily.Date.Format("2006-01-02")
		data.Accuracies[i] = daily.Accuracy
		data.UpAccuracies[i] = daily.UpAccuracy
		data.DownAccuracies[i] = daily.DownAccuracy
		data.NeutralAccuracies[i] = daily.NeutralAccuracy
		data.PredictionCounts[i] = daily.TotalCount
	}
	
	// 返回JSON响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

// GetPerformanceChartData 获取性能图表数据
func (h *VisualizationHandler) GetPerformanceChartData(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	symbol := vars["symbol"]
	
	// 获取查询参数
	daysStr := r.URL.Query().Get("days")
	days := 30
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil {
			http.Error(w, "Invalid days parameter", http.StatusBadRequest)
			return
		}
	}
	
	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)
	
	// 获取性能统计
	stats, err := h.storage.GetPerformanceStats(symbol, startTime, endTime)
	if err != nil {
		h.log.WithError(err).Error("获取性能统计失败")
		http.Error(w, "Failed to get performance stats", http.StatusInternalServerError)
		return
	}
	
	// 构建图表数据
	data := PerformanceChartData{
		Labels:          make([]string, len(stats.DailyStats)),
		Returns:         make([]float64, len(stats.DailyStats)),
		CumulativeReturns: make([]float64, len(stats.DailyStats)),
		Drawdowns:       make([]float64, len(stats.DailyStats)),
		SharpeRatios:    make([]float64, len(stats.DailyStats)),
		Timestamp:       time.Now(),
	}
	
	cumulativeReturn := 1.0
	for i, daily := range stats.DailyStats {
		data.Labels[i] = daily.Date.Format("2006-01-02")
		data.Returns[i] = daily.Return
		
		// 计算累积收益
		cumulativeReturn *= (1 + daily.Return/100)
		data.CumulativeReturns[i] = (cumulativeReturn - 1) * 100
		
		data.Drawdowns[i] = daily.Drawdown
		data.SharpeRatios[i] = daily.SharpeRatio
	}
	
	// 返回JSON响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

// GetSentimentChartData 获取舆情图表数据
func (h *VisualizationHandler) GetSentimentChartData(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	daysStr := r.URL.Query().Get("days")
	days := 7
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil {
			http.Error(w, "Invalid days parameter", http.StatusBadRequest)
			return
		}
	}
	
	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)
	
	// 获取舆情分析历史
	sentiments, err := h.storage.GetSentimentAnalysisHistory(startTime, endTime)
	if err != nil {
		h.log.WithError(err).Error("获取舆情分析历史失败")
		http.Error(w, "Failed to get sentiment analysis history", http.StatusInternalServerError)
		return
	}
	
	// 构建图表数据
	data := SentimentChartData{
		Labels:           make([]string, len(sentiments)),
		SentimentScores:  make([]float64, len(sentiments)),
		PositiveRatios:   make([]float64, len(sentiments)),
		NegativeRatios:   make([]float64, len(sentiments)),
		NeutralRatios:    make([]float64, len(sentiments)),
		InfluencerScores: make([]float64, len(sentiments)),
		Timestamp:        time.Now(),
	}
	
	for i, sentiment := range sentiments {
		data.Labels[i] = sentiment.Timestamp.Format("2006-01-02 15:04:05")
		data.SentimentScores[i] = sentiment.SentimentScore
		data.PositiveRatios[i] = sentiment.PositiveRatio
		data.NegativeRatios[i] = sentiment.NegativeRatio
		data.NeutralRatios[i] = sentiment.NeutralRatio
		data.InfluencerScores[i] = sentiment.InfluencerScore
	}
	
	// 返回JSON响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

// GetTrafficChartData 获取流量图表数据
func (h *VisualizationHandler) GetTrafficChartData(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	daysStr := r.URL.Query().Get("days")
	days := 7
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil {
			http.Error(w, "Invalid days parameter", http.StatusBadRequest)
			return
		}
	}
	
	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)
	
	// 获取流量分析历史
	traffic, err := h.storage.GetTrafficAnalysisHistory(startTime, endTime)
	if err != nil {
		h.log.WithError(err).Error("获取流量分析历史失败")
		http.Error(w, "Failed to get traffic analysis history", http.StatusInternalServerError)
		return
	}
	
	// 构建图表数据
	data := TrafficChartData{
		Labels:              make([]string, len(traffic)),
		TrafficScores:       make([]float64, len(traffic)),
		VisitorCounts:       make([]int, len(traffic)),
		SearchVolumes:       make([]int, len(traffic)),
		ReferralCounts:      make([]int, len(traffic)),
		Timestamp:           time.Now(),
	}
	
	for i, t := range traffic {
		data.Labels[i] = t.Timestamp.Format("2006-01-02 15:04:05")
		data.TrafficScores[i] = t.TrafficScore
		data.VisitorCounts[i] = t.VisitorCount
		data.SearchVolumes[i] = t.SearchVolume
		data.ReferralCounts[i] = t.ReferralCount
	}
	
	// 返回JSON响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

// GetDashboardData 获取仪表板数据
func (h *VisualizationHandler) GetDashboardData(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	symbol := r.URL.Query().Get("symbol")
	if symbol == "" {
		symbol = "BTCUSDT"
	}
	
	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -30)
	
	// 获取最新预测
	latestPrediction, err := h.storage.GetLatestPrediction(symbol)
	if err != nil {
		h.log.WithError(err).Error("获取最新预测失败")
		// 继续执行，不返回错误
	}
	
	// 获取准确率统计
	accuracyStats, err := h.storage.GetAccuracyStats(symbol, startTime, endTime)
	if err != nil {
		h.log.WithError(err).Error("获取准确率统计失败")
		// 继续执行，不返回错误
	}
	
	// 获取性能统计
	performanceStats, err := h.storage.GetPerformanceStats(symbol, startTime, endTime)
	if err != nil {
		h.log.WithError(err).Error("获取性能统计失败")
		// 继续执行，不返回错误
	}
	
	// 获取最近的预测
	recentPredictions, err := h.storage.GetPredictions(symbol, 10, 0)
	if err != nil {
		h.log.WithError(err).Error("获取最近预测失败")
		// 继续执行，不返回错误
	}
	
	// 获取最新舆情分析
	latestSentiment, err := h.storage.GetLatestSentimentAnalysis()
	if err != nil {
		h.log.WithError(err).Error("获取最新舆情分析失败")
		// 继续执行，不返回错误
	}
	
	// 获取最新流量分析
	latestTraffic, err := h.storage.GetLatestTrafficAnalysis()
	if err != nil {
		h.log.WithError(err).Error("获取最新流量分析失败")
		// 继续执行，不返回错误
	}
	
	// 构建仪表板数据
	data := DashboardData{
		LatestPrediction:    latestPrediction,
		AccuracyStats:       accuracyStats,
		PerformanceStats:    performanceStats,
		RecentPredictions:   recentPredictions,
		LatestSentiment:     latestSentiment,
		LatestTraffic:       latestTraffic,
		Timestamp:           time.Now(),
	}
	
	// 返回JSON响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(data)
}

