# BTC-Predictor 主配置文件

# 应用基本配置
app:
  name: "btc-predictor"
  version: "1.0.0"
  environment: "development" # development, testing, production
  log_level: "info" # debug, info, warn, error
  log_format: "json" # text, json

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 10s
  write_timeout: 10s
  idle_timeout: 60s

# 数据库配置
database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: "btcpredictor"
  password: "password"
  database: "btcpredictor"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 1h

# Redis配置
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10

# 数据采集配置
data_collection:
  # 市场数据采集
  market:
    enabled: true
    symbols: ["BTCUSDT"]
    intervals: ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
    max_historical_days: 30
    websocket_buffer_size: 1000
    reconnect_delay: 5s
    
  # 舆情数据采集
  sentiment:
    enabled: true
    sources: ["twitter", "reddit", "news"]
    keywords: ["bitcoin", "btc", "crypto"]
    update_interval: 10m
    
  # 流量数据采集
  traffic:
    enabled: true
    targets: ["binance.com", "coinbase.com", "kraken.com"]
    update_interval: 1h

# 预测配置
prediction:
  # 技术分析预测
  technical:
    enabled: true
    indicators: ["rsi", "macd", "bollinger", "ema", "volume"]
    
  # 机器学习预测
  ml:
    enabled: true
    models: ["xgboost", "lstm", "randomforest"]
    training_interval: 24h
    validation_split: 0.2
    
  # 舆情分析预测
  sentiment:
    enabled: true
    weight: 0.3
    
  # 综合预测
  comprehensive:
    enabled: true
    technical_weight: 0.4
    ml_weight: 0.4
    sentiment_weight: 0.2
    confidence_threshold: 0.7

# 交易配置
trading:
  enabled: false # 默认不启用自动交易
  mode: "paper" # paper, live
  symbols: ["BTCUSDT"]
  max_position_size: 0.1 # 账户余额的比例
  max_risk_per_trade: 0.02 # 每笔交易最大风险
  stop_loss_pct: 0.02 # 止损百分比
  take_profit_pct: 0.04 # 止盈百分比
  
# 回测配置
backtest:
  start_date: "2023-01-01"
  end_date: "2023-06-01"
  initial_balance: 10000
  commission_rate: 0.001

