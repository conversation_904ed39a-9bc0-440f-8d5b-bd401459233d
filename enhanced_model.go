package prediction

import (
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/analysis"
	"github.com/user/btc-predictor/internal/models"
)

// EnhancedModel 表示增强的预测模型
type EnhancedModel struct {
	log              *logrus.Logger
	name             string
	featureWeights   map[string]float64
	patternWeights   map[string]float64
	sentimentWeight  float64
	trafficWeight    float64
	volatilityWeight float64
	trendWeight      float64
	volumeWeight     float64
	confidenceThreshold float64
	minDataPoints    int
}

// NewEnhancedModel 创建一个新的增强预测模型
func NewEnhancedModel(log *logrus.Logger) *EnhancedModel {
	return &EnhancedModel{
		log:              log,
		name:             "enhanced_model",
		featureWeights:   initFeatureWeights(),
		patternWeights:   initPatternWeights(),
		sentimentWeight:  0.15,
		trafficWeight:    0.10,
		volatilityWeight: 0.15,
		trendWeight:      0.40,
		volumeWeight:     0.20,
		confidenceThreshold: 0.65,
		minDataPoints:    100,
	}
}

// Name 返回模型名称
func (m *EnhancedModel) Name() string {
	return m.name
}

// Predict 使用增强模型进行预测
func (m *EnhancedModel) Predict(
	klines []models.KLineData,
	indicators map[string][]float64,
	features []analysis.Feature,
	sentiments []models.SentimentData,
	influencerPosts []models.InfluencerPost,
	trafficData []models.TrafficData,
	searchTrends []models.SearchTrendData,
	referralTraffic []models.ReferralTraffic,
	targetTimeMinutes int,
) (*PredictionResult, error) {
	// 检查数据点数量是否足够
	if len(klines) < m.minDataPoints {
		return nil, fmt.Errorf("数据点数量不足，需要至少%d个，实际%d个", m.minDataPoints, len(klines))
	}

	// 将特征转换为映射
	featureMap := analysis.FeaturesToMap(features)

	// 1. 技术分析评分
	technicalScore := m.calculateTechnicalScore(featureMap)

	// 2. 价格模式评分
	patternScore := m.calculatePatternScore(featureMap)

	// 3. 趋势强度评分
	trendScore := m.calculateTrendScore(featureMap, klines)

	// 4. 波动性评分
	volatilityScore := m.calculateVolatilityScore(featureMap, klines)

	// 5. 成交量评分
	volumeScore := m.calculateVolumeScore(featureMap, klines)

	// 6. 舆情评分
	sentimentScore := m.calculateSentimentScore(sentiments, influencerPosts)

	// 7. 流量评分
	trafficScore := m.calculateTrafficScore(trafficData, searchTrends, referralTraffic)

	// 计算加权总分
	weightedScore := m.trendWeight*trendScore +
		m.volatilityWeight*volatilityScore +
		m.volumeWeight*volumeScore +
		m.sentimentWeight*sentimentScore +
		m.trafficWeight*trafficScore

	// 调整技术分析和价格模式的权重
	technicalWeight := 0.7
	patternWeight := 0.3
	technicalAndPatternScore := technicalWeight*technicalScore + patternWeight*patternScore

	// 综合评分
	finalScore := 0.7*technicalAndPatternScore + 0.3*weightedScore

	// 确定预测方向和变化幅度
	var direction Direction
	var predictedChange float64
	var confidence float64

	if finalScore > 0.2 {
		direction = Up
		predictedChange = math.Min(finalScore*2.0, 5.0) // 最大预测变化为5%
		confidence = math.Min(0.5+finalScore*0.5, 0.95) // 置信度范围：0.5-0.95
	} else if finalScore < -0.2 {
		direction = Down
		predictedChange = math.Max(finalScore*2.0, -5.0) // 最大预测变化为-5%
		confidence = math.Min(0.5+math.Abs(finalScore)*0.5, 0.95) // 置信度范围：0.5-0.95
	} else {
		direction = Neutral
		predictedChange = finalScore
		confidence = 0.5 + math.Abs(finalScore)*0.5 // 置信度范围：0.5-0.75
	}

	// 如果置信度不够，返回中性预测
	if confidence < m.confidenceThreshold {
		m.log.WithFields(logrus.Fields{
			"original_direction": direction,
			"confidence":         confidence,
			"threshold":          m.confidenceThreshold,
		}).Debug("置信度不足，返回中性预测")
		
		direction = Neutral
		predictedChange = 0.0
		confidence = 0.5
	}

	// 创建预测结果
	result := &PredictionResult{
		Direction:       direction,
		PredictedChange: predictedChange,
		Confidence:      confidence,
		ModelName:       m.name,
		Features:        featureMap,
		Scores: map[string]float64{
			"technical_score":  technicalScore,
			"pattern_score":    patternScore,
			"trend_score":      trendScore,
			"volatility_score": volatilityScore,
			"volume_score":     volumeScore,
			"sentiment_score":  sentimentScore,
			"traffic_score":    trafficScore,
			"final_score":      finalScore,
		},
	}

	return result, nil
}

// calculateTechnicalScore 计算技术分析评分
func (m *EnhancedModel) calculateTechnicalScore(features map[string]float64) float64 {
	score := 0.0
	count := 0

	// 遍历所有特征
	for name, weight := range m.featureWeights {
		if value, ok := features[name]; ok {
			score += value * weight
			count++
		}
	}

	// 如果没有特征，返回0
	if count == 0 {
		return 0.0
	}

	// 归一化评分到[-1, 1]范围
	normalizedScore := math.Max(math.Min(score/float64(count), 1.0), -1.0)
	
	return normalizedScore
}

// calculatePatternScore 计算价格模式评分
func (m *EnhancedModel) calculatePatternScore(features map[string]float64) float64 {
	score := 0.0
	count := 0

	// 遍历所有价格模式
	for name, weight := range m.patternWeights {
		if value, ok := features[name]; ok {
			score += value * weight
			count++
		}
	}

	// 如果没有价格模式，返回0
	if count == 0 {
		return 0.0
	}

	// 归一化评分到[-1, 1]范围
	normalizedScore := math.Max(math.Min(score/float64(count), 1.0), -1.0)
	
	return normalizedScore
}

// calculateTrendScore 计算趋势强度评分
func (m *EnhancedModel) calculateTrendScore(features map[string]float64, klines []models.KLineData) float64 {
	// 使用多个时间周期的移动平均线判断趋势
	shortTermTrend := 0.0
	mediumTermTrend := 0.0
	longTermTrend := 0.0

	// 短期趋势：EMA10 vs EMA20
	if ema10, ok := features["ema10"]; ok {
		if ema20, ok := features["ema20"]; ok {
			shortTermTrend = (ema10 - ema20) / ema20 * 100
		}
	}

	// 中期趋势：EMA20 vs EMA50
	if ema20, ok := features["ema20"]; ok {
		if ema50, ok := features["ema50"]; ok {
			mediumTermTrend = (ema20 - ema50) / ema50 * 100
		}
	}

	// 长期趋势：EMA50 vs EMA100
	if ema50, ok := features["ema50"]; ok {
		if ema100, ok := features["ema100"]; ok {
			longTermTrend = (ema50 - ema100) / ema100 * 100
		}
	}

	// 加权计算趋势得分
	trendScore := 0.5*shortTermTrend + 0.3*mediumTermTrend + 0.2*longTermTrend

	// 归一化到[-1, 1]范围
	normalizedScore := math.Max(math.Min(trendScore/2.0, 1.0), -1.0)
	
	return normalizedScore
}

// calculateVolatilityScore 计算波动性评分
func (m *EnhancedModel) calculateVolatilityScore(features map[string]float64, klines []models.KLineData) float64 {
	// 使用ATR和布林带宽度评估波动性
	atrScore := 0.0
	bbWidthScore := 0.0

	// ATR相对值
	if atr, ok := features["atr14"]; ok {
		if close, ok := features["close"]; ok && close > 0 {
			atrScore = atr / close * 100
		}
	}

	// 布林带宽度
	if bbWidth, ok := features["bb_width"]; ok {
		bbWidthScore = bbWidth * 100
	}

	// 计算波动性得分
	volatilityScore := 0.6*atrScore + 0.4*bbWidthScore

	// 将波动性转换为方向性信号
	// 高波动性在上升趋势中是积极信号，在下降趋势中是消极信号
	if trendDirection, ok := features["trend_direction"]; ok {
		volatilityScore *= trendDirection
	}

	// 归一化到[-1, 1]范围
	normalizedScore := math.Max(math.Min(volatilityScore/5.0, 1.0), -1.0)
	
	return normalizedScore
}

// calculateVolumeScore 计算成交量评分
func (m *EnhancedModel) calculateVolumeScore(features map[string]float64, klines []models.KLineData) float64 {
	// 使用成交量和价格变化的关系评估成交量
	volumeChangeScore := 0.0
	volumePriceRelationScore := 0.0

	// 成交量变化
	if volumeChange, ok := features["volume_change"]; ok {
		volumeChangeScore = volumeChange
	}

	// 成交量与价格关系
	if volumePriceRelation, ok := features["volume_price_relation"]; ok {
		volumePriceRelationScore = volumePriceRelation
	}

	// 计算成交量得分
	volumeScore := 0.4*volumeChangeScore + 0.6*volumePriceRelationScore

	// 归一化到[-1, 1]范围
	normalizedScore := math.Max(math.Min(volumeScore, 1.0), -1.0)
	
	return normalizedScore
}

// calculateSentimentScore 计算舆情评分
func (m *EnhancedModel) calculateSentimentScore(sentiments []models.SentimentData, influencerPosts []models.InfluencerPost) float64 {
	if len(sentiments) == 0 && len(influencerPosts) == 0 {
		return 0.0
	}

	// 计算一般舆情得分
	generalSentimentScore := 0.0
	if len(sentiments) > 0 {
		totalScore := 0.0
		totalWeight := 0.0
		
		// 按时间排序，最近的舆情权重更高
		sort.Slice(sentiments, func(i, j int) bool {
			return sentiments[i].Timestamp.After(sentiments[j].Timestamp)
		})
		
		// 计算加权平均
		for i, sentiment := range sentiments {
			// 时间衰减权重
			timeWeight := math.Exp(-float64(i) * 0.1)
			
			// 来源可信度权重
			sourceWeight := sentiment.SourceCredibility
			
			// 互动量权重
			engagementWeight := math.Log1p(float64(sentiment.Engagement)) / 10.0
			
			// 总权重
			weight := timeWeight * (0.6*sourceWeight + 0.4*engagementWeight)
			
			// 情感得分：正面(1.0)，中性(0.0)，负面(-1.0)
			var sentimentValue float64
			switch sentiment.Sentiment {
			case "positive":
				sentimentValue = 1.0
			case "neutral":
				sentimentValue = 0.0
			case "negative":
				sentimentValue = -1.0
			}
			
			totalScore += sentimentValue * weight
			totalWeight += weight
		}
		
		if totalWeight > 0 {
			generalSentimentScore = totalScore / totalWeight
		}
	}

	// 计算有影响力人物的舆情得分
	influencerSentimentScore := 0.0
	if len(influencerPosts) > 0 {
		totalScore := 0.0
		totalWeight := 0.0
		
		// 按时间排序，最近的发布内容权重更高
		sort.Slice(influencerPosts, func(i, j int) bool {
			return influencerPosts[i].Timestamp.After(influencerPosts[j].Timestamp)
		})
		
		// 计算加权平均
		for i, post := range influencerPosts {
			// 时间衰减权重
			timeWeight := math.Exp(-float64(i) * 0.2)
			
			// 影响力权重
			influenceWeight := post.InfluencerScore
			
			// 互动量权重
			engagementWeight := math.Log1p(float64(post.Engagement)) / 10.0
			
			// 总权重
			weight := timeWeight * (0.7*influenceWeight + 0.3*engagementWeight)
			
			// 情感得分：正面(1.0)，中性(0.0)，负面(-1.0)
			var sentimentValue float64
			switch post.Sentiment {
			case "positive":
				sentimentValue = 1.0
			case "neutral":
				sentimentValue = 0.0
			case "negative":
				sentimentValue = -1.0
			}
			
			totalScore += sentimentValue * weight
			totalWeight += weight
		}
		
		if totalWeight > 0 {
			influencerSentimentScore = totalScore / totalWeight
		}
	}

	// 综合舆情得分
	var sentimentScore float64
	if len(sentiments) > 0 && len(influencerPosts) > 0 {
		sentimentScore = 0.4*generalSentimentScore + 0.6*influencerSentimentScore
	} else if len(sentiments) > 0 {
		sentimentScore = generalSentimentScore
	} else {
		sentimentScore = influencerSentimentScore
	}

	// 归一化到[-1, 1]范围
	normalizedScore := math.Max(math.Min(sentimentScore, 1.0), -1.0)
	
	return normalizedScore
}

// calculateTrafficScore 计算流量评分
func (m *EnhancedModel) calculateTrafficScore(trafficData []models.TrafficData, searchTrends []models.SearchTrendData, referralTraffic []models.ReferralTraffic) float64 {
	if len(trafficData) == 0 && len(searchTrends) == 0 && len(referralTraffic) == 0 {
		return 0.0
	}

	// 计算网站流量得分
	websiteTrafficScore := 0.0
	if len(trafficData) > 0 {
		totalScore := 0.0
		totalWeight := 0.0
		
		// 按时间排序，最近的流量数据权重更高
		sort.Slice(trafficData, func(i, j int) bool {
			return trafficData[i].Timestamp.After(trafficData[j].Timestamp)
		})
		
		// 计算加权平均
		for i, traffic := range trafficData {
			// 时间衰减权重
			timeWeight := math.Exp(-float64(i) * 0.1)
			
			// 流量变化率
			trafficChangeRate := traffic.VisitorsChangeRate
			
			// 停留时间变化率
			timeOnSiteChangeRate := traffic.TimeOnSiteChangeRate
			
			// 跳出率变化（负值表示跳出率下降，是积极信号）
			bounceRateChange := -traffic.BounceRateChangeRate
			
			// 计算流量得分
			trafficScore := 0.5*trafficChangeRate + 0.3*timeOnSiteChangeRate + 0.2*bounceRateChange
			
			totalScore += trafficScore * timeWeight
			totalWeight += timeWeight
		}
		
		if totalWeight > 0 {
			websiteTrafficScore = totalScore / totalWeight
		}
	}

	// 计算搜索趋势得分
	searchTrendScore := 0.0
	if len(searchTrends) > 0 {
		totalScore := 0.0
		totalWeight := 0.0
		
		// 按时间排序，最近的搜索趋势数据权重更高
		sort.Slice(searchTrends, func(i, j int) bool {
			return searchTrends[i].Timestamp.After(searchTrends[j].Timestamp)
		})
		
		// 计算加权平均
		for i, trend := range searchTrends {
			// 时间衰减权重
			timeWeight := math.Exp(-float64(i) * 0.1)
			
			// 搜索量变化率
			searchVolumeChangeRate := trend.SearchVolumeChangeRate
			
			totalScore += searchVolumeChangeRate * timeWeight
			totalWeight += timeWeight
		}
		
		if totalWeight > 0 {
			searchTrendScore = totalScore / totalWeight
		}
	}

	// 计算引荐流量得分
	referralTrafficScore := 0.0
	if len(referralTraffic) > 0 {
		totalScore := 0.0
		totalWeight := 0.0
		
		// 按时间排序，最近的引荐流量数据权重更高
		sort.Slice(referralTraffic, func(i, j int) bool {
			return referralTraffic[i].Timestamp.After(referralTraffic[j].Timestamp)
		})
		
		// 计算加权平均
		for i, referral := range referralTraffic {
			// 时间衰减权重
			timeWeight := math.Exp(-float64(i) * 0.1)
			
			// 引荐流量变化率
			referralChangeRate := referral.ReferralChangeRate
			
			totalScore += referralChangeRate * timeWeight
			totalWeight += timeWeight
		}
		
		if totalWeight > 0 {
			referralTrafficScore = totalScore / totalWeight
		}
	}

	// 综合流量得分
	trafficScore := 0.0
	weightSum := 0.0
	
	if len(trafficData) > 0 {
		trafficScore += 0.4 * websiteTrafficScore
		weightSum += 0.4
	}
	
	if len(searchTrends) > 0 {
		trafficScore += 0.4 * searchTrendScore
		weightSum += 0.4
	}
	
	if len(referralTraffic) > 0 {
		trafficScore += 0.2 * referralTrafficScore
		weightSum += 0.2
	}
	
	if weightSum > 0 {
		trafficScore /= weightSum
	}

	// 归一化到[-1, 1]范围
	normalizedScore := math.Max(math.Min(trafficScore, 1.0), -1.0)
	
	return normalizedScore
}

// Train 训练模型
func (m *EnhancedModel) Train(
	klines []models.KLineData,
	indicators map[string][]float64,
	features [][]analysis.Feature,
	sentiments []models.SentimentData,
	influencerPosts []models.InfluencerPost,
	trafficData []models.TrafficData,
	searchTrends []models.SearchTrendData,
	referralTraffic []models.ReferralTraffic,
	targetPrices []float64,
) error {
	// 这里可以实现模型训练逻辑
	// 例如，使用历史数据和目标价格调整特征权重
	
	m.log.Info("增强模型训练完成")
	return nil
}

// Save 保存模型
func (m *EnhancedModel) Save(path string) error {
	// 这里可以实现模型保存逻辑
	m.log.WithField("path", path).Info("增强模型保存完成")
	return nil
}

// Load 加载模型
func (m *EnhancedModel) Load(path string) error {
	// 这里可以实现模型加载逻辑
	m.log.WithField("path", path).Info("增强模型加载完成")
	return nil
}

// initFeatureWeights 初始化特征权重
func initFeatureWeights() map[string]float64 {
	return map[string]float64{
		// 趋势指标
		"sma5_cross_sma10":     0.8,  // SMA5穿过SMA10
		"sma10_cross_sma20":    0.6,  // SMA10穿过SMA20
		"ema5_cross_ema10":     0.9,  // EMA5穿过EMA10
		"ema10_cross_ema20":    0.7,  // EMA10穿过EMA20
		"price_above_sma20":    0.5,  // 价格在SMA20上方
		"price_above_ema20":    0.6,  // 价格在EMA20上方
		"macd_histogram":       0.8,  // MACD柱状图
		"macd_signal_cross":    0.9,  // MACD信号线交叉
		"adx14":                0.7,  // ADX指标
		"adx_trend_strength":   0.6,  // ADX趋势强度
		"supertrend":           0.8,  // 超级趋势指标
		"parabolic_sar_signal": 0.7,  // 抛物线转向信号

		// 动量指标
		"rsi14":                0.9,  // RSI指标
		"rsi_overbought":       0.8,  // RSI超买
		"rsi_oversold":         0.8,  // RSI超卖
		"stoch_k":              0.6,  // 随机震荡指标K值
		"stoch_d":              0.6,  // 随机震荡指标D值
		"stoch_cross":          0.7,  // 随机震荡指标交叉
		"cci20":                0.5,  // CCI指标
		"mfi14":                0.6,  // MFI指标
		"williams_r":           0.5,  // 威廉指标

		// 波动性指标
		"bb_width":             0.6,  // 布林带宽度
		"bb_position":          0.7,  // 价格在布林带中的位置
		"bb_squeeze":           0.8,  // 布林带挤压
		"atr14":                0.5,  // ATR指标
		"keltner_position":     0.6,  // 价格在肯特纳通道中的位置

		// 成交量指标
		"obv":                  0.7,  // OBV指标
		"obv_trend":            0.8,  // OBV趋势
		"volume_sma_ratio":     0.6,  // 成交量与其SMA的比率
		"vwap_position":        0.7,  // 价格相对于VWAP的位置
		"volume_price_trend":   0.8,  // 成交量价格趋势

		// 价格动量
		"price_momentum":       0.9,  // 价格动量
		"price_rate_of_change": 0.8,  // 价格变化率
		"price_acceleration":   0.7,  // 价格加速度
	}
}

// initPatternWeights 初始化价格模式权重
func initPatternWeights() map[string]float64 {
	return map[string]float64{
		// 反转模式
		"hammer":               0.8,  // 锤子线
		"inverted_hammer":      0.7,  // 倒锤子线
		"shooting_star":        0.8,  // 流星线
		"hanging_man":          0.7,  // 上吊线
		"engulfing_bullish":    0.9,  // 看涨吞没形态
		"engulfing_bearish":    0.9,  // 看跌吞没形态
		"morning_star":         0.9,  // 晨星形态
		"evening_star":         0.9,  // 暮星形态
		"doji":                 0.5,  // 十字星
		"dragonfly_doji":       0.7,  // 蜻蜓十字星
		"gravestone_doji":      0.7,  // 墓碑十字星

		// 持续模式
		"three_white_soldiers": 0.8,  // 三白兵
		"three_black_crows":    0.8,  // 三黑鸦
		"rising_three":         0.7,  // 上升三法
		"falling_three":        0.7,  // 下降三法
		"bullish_harami":       0.6,  // 看涨孕线
		"bearish_harami":       0.6,  // 看跌孕线

		// 支撑阻力突破
		"resistance_breakout":  0.9,  // 阻力突破
		"support_breakdown":    0.9,  // 支撑击穿
		"resistance_test":      0.7,  // 阻力测试
		"support_test":         0.7,  // 支撑测试
		"double_top":           0.8,  // 双顶
		"double_bottom":        0.8,  // 双底
		"head_shoulders":       0.8,  // 头肩顶
		"inv_head_shoulders":   0.8,  // 头肩底
	}
}

