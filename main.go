package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/storage"
	"github.com/user/btc-predictor/pkg/binance"
	"github.com/user/btc-predictor/pkg/config"
	"github.com/user/btc-predictor/pkg/logger"
)

var (
	configPath  string
	logLevel    string
	logFormat   string
	useTestnet  bool
	symbol      string
	interval    string
	startDate   string
	endDate     string
	mode        string
)

func init() {
	// 解析命令行参数
	flag.StringVar(&configPath, "config", "config/config.yaml", "配置文件路径")
	flag.StringVar(&logLevel, "log-level", "info", "日志级别 (debug, info, warn, error)")
	flag.StringVar(&logFormat, "log-format", "text", "日志格式 (text, json)")
	flag.BoolVar(&useTestnet, "testnet", false, "是否使用测试网络")
	flag.StringVar(&symbol, "symbol", "BTCUSDT", "交易对符号")
	flag.StringVar(&interval, "interval", "1m", "K线间隔")
	flag.StringVar(&startDate, "start", "", "开始日期 (YYYY-MM-DD)")
	flag.StringVar(&endDate, "end", "", "结束日期 (YYYY-MM-DD)")
	flag.StringVar(&mode, "mode", "collect", "运行模式 (collect, predict, backtest, trade)")
}

func main() {
	// 解析命令行参数
	flag.Parse()

	// 初始化日志
	log := logger.New(logger.Config{
		Level:  logLevel,
		Format: logFormat,
		Output: "",
	})

	log.Info("启动BTC-Predictor")

	// 加载配置
	cfg := config.New(log)
	if err := cfg.LoadConfig(configPath); err != nil {
		log.WithError(err).Fatal("加载配置失败")
	}

	// 创建数据存储
	mysqlCfg := storage.MySQLConfig{
		Host:     cfg.GetString("database.host"),
		Port:     cfg.GetInt("database.port"),
		Username: cfg.GetString("database.username"),
		Password: cfg.GetString("database.password"),
		Database: cfg.GetString("database.database"),
		Log:      log,
	}

	store, err := storage.NewMySQLStorage(mysqlCfg)
	if err != nil {
		log.WithError(err).Fatal("创建数据存储失败")
	}

	// 创建币安客户端
	binanceCfg := binance.ClientConfig{
		APIKey:     cfg.GetString("binance.api.api_key"),
		APISecret:  cfg.GetString("binance.api.api_secret"),
		UseTestnet: useTestnet || cfg.GetBool("binance.api.use_testnet"),
		Log:        log,
		Config:     cfg,
	}

	client := binance.New(binanceCfg)

	// 根据运行模式执行不同的操作
	switch mode {
	case "collect":
		runDataCollection(client, store, cfg, log)
	case "predict":
		runPrediction(client, store, cfg, log)
	case "backtest":
		runBacktest(client, store, cfg, log)
	case "trade":
		runTrading(client, store, cfg, log)
	default:
		log.Fatalf("未知的运行模式: %s", mode)
	}
}

// runDataCollection 运行数据采集
func runDataCollection(client *binance.Client, store *storage.MySQLStorage, cfg *config.Config, log *logrus.Logger) {
	log.Info("启动数据采集模式")

	// 获取配置
	symbols := cfg.GetStringSlice("data_collection.market.symbols")
	if len(symbols) == 0 {
		symbols = []string{symbol}
	}

	intervals := cfg.GetStringSlice("data_collection.market.intervals")
	if len(intervals) == 0 {
		intervals = []string{interval}
	}

	bufferSize := cfg.GetInt("data_collection.market.websocket_buffer_size")
	if bufferSize <= 0 {
		bufferSize = 1000
	}

	collectDepth := cfg.GetBool("data_collection.market.collect_depth")
	collectTrade := cfg.GetBool("data_collection.market.collect_trade")

	// 创建数据采集器
	collector := binance.NewDataCollector(binance.CollectorConfig{
		Client:       client,
		Log:          log,
		Storage:      store,
		Symbols:      symbols,
		Intervals:    intervals,
		BufferSize:   bufferSize,
		CollectDepth: collectDepth,
		CollectTrade: collectTrade,
	})

	// 启动数据采集
	if err := collector.Start(); err != nil {
		log.WithError(err).Fatal("启动数据采集失败")
	}

	// 如果指定了开始和结束日期，获取历史数据
	if startDate != "" && endDate != "" {
		startTime, err := time.Parse("2006-01-02", startDate)
		if err != nil {
			log.WithError(err).Fatal("解析开始日期失败")
		}

		endTime, err := time.Parse("2006-01-02", endDate)
		if err != nil {
			log.WithError(err).Fatal("解析结束日期失败")
		}

		// 为每个交易对和间隔获取历史数据
		for _, sym := range symbols {
			for _, itv := range intervals {
				log.WithFields(logrus.Fields{
					"symbol":    sym,
					"interval":  itv,
					"startTime": startTime,
					"endTime":   endTime,
				}).Info("获取历史数据")

				if err := collector.FetchHistoricalKlines(sym, itv, startTime, endTime); err != nil {
					log.WithError(err).Error("获取历史数据失败")
				}
			}
		}
	}

	// 等待信号
	waitForSignal(func() {
		collector.Stop()
		if err := store.Close(); err != nil {
			log.WithError(err).Error("关闭数据存储失败")
		}
	})
}

// runPrediction 运行预测
func runPrediction(client *binance.Client, store *storage.MySQLStorage, cfg *config.Config, log *logrus.Logger) {
	log.Info("启动预测模式")
	log.Warn("预测模式尚未实现")

	// 等待信号
	waitForSignal(func() {
		if err := store.Close(); err != nil {
			log.WithError(err).Error("关闭数据存储失败")
		}
	})
}

// runBacktest 运行回测
func runBacktest(client *binance.Client, store *storage.MySQLStorage, cfg *config.Config, log *logrus.Logger) {
	log.Info("启动回测模式")
	log.Warn("回测模式尚未实现")

	// 等待信号
	waitForSignal(func() {
		if err := store.Close(); err != nil {
			log.WithError(err).Error("关闭数据存储失败")
		}
	})
}

// runTrading 运行交易
func runTrading(client *binance.Client, store *storage.MySQLStorage, cfg *config.Config, log *logrus.Logger) {
	log.Info("启动交易模式")
	log.Warn("交易模式尚未实现")

	// 等待信号
	waitForSignal(func() {
		if err := store.Close(); err != nil {
			log.WithError(err).Error("关闭数据存储失败")
		}
	})
}

// waitForSignal 等待信号
func waitForSignal(cleanup func()) {
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	sig := <-sigChan
	fmt.Printf("\n收到信号: %v，正在退出...\n", sig)

	// 执行清理操作
	if cleanup != nil {
		cleanup()
	}
}

// getExecutablePath 获取可执行文件路径
func getExecutablePath() string {
	ex, err := os.Executable()
	if err != nil {
		return ""
	}
	return filepath.Dir(ex)
}

