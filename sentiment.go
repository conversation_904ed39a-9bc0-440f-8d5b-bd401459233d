package models

import (
	"time"
)

// SentimentData 表示舆情数据
type SentimentData struct {
	ID             uint      `gorm:"primaryKey" json:"-"`
	Source         string    `gorm:"index:idx_source_time" json:"source"` // twitter, reddit, news, etc.
	Timestamp      time.Time `gorm:"index:idx_source_time" json:"timestamp"`
	Text           string    `gorm:"type:text" json:"text"`
	SentimentScore float64   `json:"sentiment_score"` // -1.0 to 1.0
	Influence      float64   `json:"influence"`       // 影响力指数
	Keywords       string    `gorm:"type:json" json:"-"`
	KeywordsList   []string  `gorm:"-" json:"keywords"`
	URL            string    `json:"url"`
	Author         string    `json:"author"`
	Likes          int       `json:"likes"`
	Shares         int       `json:"shares"`
	Comments       int       `json:"comments"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// TableName 返回SentimentData的表名
func (SentimentData) TableName() string {
	return "sentiment_data"
}

// AggregatedSentiment 表示聚合的舆情数据
type AggregatedSentiment struct {
	ID            uint      `gorm:"primaryKey" json:"-"`
	Symbol        string    `gorm:"index:idx_symbol_time" json:"symbol"`
	Timestamp     time.Time `gorm:"index:idx_symbol_time" json:"timestamp"`
	OverallScore  float64   `json:"overall_score"`
	PositiveCount int       `json:"positive_count"`
	NegativeCount int       `json:"negative_count"`
	NeutralCount  int       `json:"neutral_count"`
	TotalCount    int       `json:"total_count"`
	Sources       string    `gorm:"type:json" json:"-"`
	SourcesList   []string  `gorm:"-" json:"sources"`
	TimeWindow    string    `json:"time_window"` // 1h, 4h, 1d, etc.
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 返回AggregatedSentiment的表名
func (AggregatedSentiment) TableName() string {
	return "aggregated_sentiment"
}

// KeywordTrend 表示关键词趋势数据
type KeywordTrend struct {
	ID         uint      `gorm:"primaryKey" json:"-"`
	Keyword    string    `gorm:"index:idx_keyword_time" json:"keyword"`
	Timestamp  time.Time `gorm:"index:idx_keyword_time" json:"timestamp"`
	Frequency  int       `json:"frequency"`
	Source     string    `json:"source"`
	TimeWindow string    `json:"time_window"` // 1h, 4h, 1d, etc.
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// TableName 返回KeywordTrend的表名
func (KeywordTrend) TableName() string {
	return "keyword_trend"
}

// InfluencerPost 表示有影响力的人物发布的内容
type InfluencerPost struct {
	ID             uint      `gorm:"primaryKey" json:"-"`
	Source         string    `gorm:"index:idx_source_time" json:"source"`
	Timestamp      time.Time `gorm:"index:idx_source_time" json:"timestamp"`
	InfluencerName string    `json:"influencer_name"`
	InfluencerID   string    `json:"influencer_id"`
	Text           string    `gorm:"type:text" json:"text"`
	SentimentScore float64   `json:"sentiment_score"`
	URL            string    `json:"url"`
	Likes          int       `json:"likes"`
	Shares         int       `json:"shares"`
	Comments       int       `json:"comments"`
	Keywords       string    `gorm:"type:json" json:"-"`
	KeywordsList   []string  `gorm:"-" json:"keywords"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// TableName 返回InfluencerPost的表名
func (InfluencerPost) TableName() string {
	return "influencer_posts"
}

