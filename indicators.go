package analysis

import (
	"fmt"
	"math"
	"time"

	"github.com/cinar/indicator/v2/helper"
	"github.com/cinar/indicator/v2/momentum"
	"github.com/cinar/indicator/v2/trend"
	"github.com/cinar/indicator/v2/volatility"
	"github.com/cinar/indicator/v2/volume"
	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/models"
)

// IndicatorCalculator 表示技术指标计算器
type IndicatorCalculator struct {
	log *logrus.Logger
}

// NewIndicatorCalculator 创建一个新的技术指标计算器
func NewIndicatorCalculator(log *logrus.Logger) *IndicatorCalculator {
	return &IndicatorCalculator{
		log: log,
	}
}

// CalculateIndicators 计算所有技术指标
func (c *IndicatorCalculator) CalculateIndicators(klines []models.KLineData) (map[string][]float64, error) {
	if len(klines) == 0 {
		return nil, fmt.Errorf("没有K线数据")
	}

	// 准备数据
	opens := make([]float64, len(klines))
	highs := make([]float64, len(klines))
	lows := make([]float64, len(klines))
	closes := make([]float64, len(klines))
	volumes := make([]float64, len(klines))
	timestamps := make([]time.Time, len(klines))

	for i, kline := range klines {
		opens[i] = kline.Open
		highs[i] = kline.High
		lows[i] = kline.Low
		closes[i] = kline.Close
		volumes[i] = kline.Volume
		timestamps[i] = kline.OpenTime
	}

	// 计算指标
	indicators := make(map[string][]float64)

	// 趋势指标
	c.calculateTrendIndicators(indicators, opens, highs, lows, closes, volumes)

	// 动量指标
	c.calculateMomentumIndicators(indicators, opens, highs, lows, closes, volumes)

	// 波动性指标
	c.calculateVolatilityIndicators(indicators, opens, highs, lows, closes, volumes)

	// 成交量指标
	c.calculateVolumeIndicators(indicators, opens, highs, lows, closes, volumes)

	return indicators, nil
}

// calculateTrendIndicators 计算趋势指标
func (c *IndicatorCalculator) calculateTrendIndicators(indicators map[string][]float64, opens, highs, lows, closes, volumes []float64) {
	// 简单移动平均线 (SMA)
	sma5 := helper.ChanToSlice(trend.Sma(helper.SliceToChan(closes), 5))
	sma10 := helper.ChanToSlice(trend.Sma(helper.SliceToChan(closes), 10))
	sma20 := helper.ChanToSlice(trend.Sma(helper.SliceToChan(closes), 20))
	sma50 := helper.ChanToSlice(trend.Sma(helper.SliceToChan(closes), 50))
	sma200 := helper.ChanToSlice(trend.Sma(helper.SliceToChan(closes), 200))

	indicators["sma5"] = sma5
	indicators["sma10"] = sma10
	indicators["sma20"] = sma20
	indicators["sma50"] = sma50
	indicators["sma200"] = sma200

	// 指数移动平均线 (EMA)
	ema5 := helper.ChanToSlice(trend.Ema(helper.SliceToChan(closes), 5))
	ema10 := helper.ChanToSlice(trend.Ema(helper.SliceToChan(closes), 10))
	ema20 := helper.ChanToSlice(trend.Ema(helper.SliceToChan(closes), 20))
	ema50 := helper.ChanToSlice(trend.Ema(helper.SliceToChan(closes), 50))
	ema200 := helper.ChanToSlice(trend.Ema(helper.SliceToChan(closes), 200))

	indicators["ema5"] = ema5
	indicators["ema10"] = ema10
	indicators["ema20"] = ema20
	indicators["ema50"] = ema50
	indicators["ema200"] = ema200

	// 移动平均收敛散度 (MACD)
	macdLine, signalLine, histogram := trend.Macd(helper.SliceToChan(closes), 12, 26, 9)
	indicators["macd_line"] = helper.ChanToSlice(macdLine)
	indicators["macd_signal"] = helper.ChanToSlice(signalLine)
	indicators["macd_histogram"] = helper.ChanToSlice(histogram)

	// 抛物线转向 (Parabolic SAR)
	sar := helper.ChanToSlice(trend.ParabolicSar(
		helper.SliceToChan(highs),
		helper.SliceToChan(lows),
		0.02, 0.2))
	indicators["parabolic_sar"] = sar

	// 典型价格
	typicalPrice := helper.ChanToSlice(trend.TypicalPrice(
		helper.SliceToChan(highs),
		helper.SliceToChan(lows),
		helper.SliceToChan(closes)))
	indicators["typical_price"] = typicalPrice
}

// calculateMomentumIndicators 计算动量指标
func (c *IndicatorCalculator) calculateMomentumIndicators(indicators map[string][]float64, opens, highs, lows, closes, volumes []float64) {
	// 相对强弱指数 (RSI)
	rsi14 := helper.ChanToSlice(momentum.Rsi(helper.SliceToChan(closes), 14))
	indicators["rsi14"] = rsi14

	// 随机震荡指标 (Stochastic Oscillator)
	k, d := momentum.StochasticOscillator(
		helper.SliceToChan(highs),
		helper.SliceToChan(lows),
		helper.SliceToChan(closes),
		14, 3, 3)
	indicators["stoch_k"] = helper.ChanToSlice(k)
	indicators["stoch_d"] = helper.ChanToSlice(d)

	// 随机RSI
	stochRsi := helper.ChanToSlice(momentum.StochasticRsi(helper.SliceToChan(closes), 14, 14, 3, 3))
	indicators["stoch_rsi"] = stochRsi

	// 威廉指标 (Williams %R)
	williamsR := helper.ChanToSlice(momentum.WilliamsR(
		helper.SliceToChan(highs),
		helper.SliceToChan(lows),
		helper.SliceToChan(closes),
		14))
	indicators["williams_r"] = williamsR

	// 真实强度指数 (TSI)
	tsi := helper.ChanToSlice(momentum.Tsi(helper.SliceToChan(closes), 25, 13))
	indicators["tsi"] = tsi
}

// calculateVolatilityIndicators 计算波动性指标
func (c *IndicatorCalculator) calculateVolatilityIndicators(indicators map[string][]float64, opens, highs, lows, closes, volumes []float64) {
	// 真实波动幅度 (ATR)
	atr := helper.ChanToSlice(volatility.Atr(
		helper.SliceToChan(highs),
		helper.SliceToChan(lows),
		helper.SliceToChan(closes),
		14))
	indicators["atr"] = atr

	// 布林带 (Bollinger Bands)
	upper, middle, lower := volatility.BollingerBands(helper.SliceToChan(closes), 20, 2)
	indicators["bb_upper"] = helper.ChanToSlice(upper)
	indicators["bb_middle"] = helper.ChanToSlice(middle)
	indicators["bb_lower"] = helper.ChanToSlice(lower)

	// 布林带宽度
	bbw := helper.ChanToSlice(volatility.BollingerBandWidth(helper.SliceToChan(closes), 20, 2))
	indicators["bb_width"] = bbw

	// %B 指标
	percentB := helper.ChanToSlice(volatility.PercentB(helper.SliceToChan(closes), 20, 2))
	indicators["percent_b"] = percentB

	// 肯特纳通道 (Keltner Channel)
	keltnerUpper, keltnerMiddle, keltnerLower := volatility.KeltnerChannel(
		helper.SliceToChan(highs),
		helper.SliceToChan(lows),
		helper.SliceToChan(closes),
		20, 10, 2)
	indicators["kc_upper"] = helper.ChanToSlice(keltnerUpper)
	indicators["kc_middle"] = helper.ChanToSlice(keltnerMiddle)
	indicators["kc_lower"] = helper.ChanToSlice(keltnerLower)
}

// calculateVolumeIndicators 计算成交量指标
func (c *IndicatorCalculator) calculateVolumeIndicators(indicators map[string][]float64, opens, highs, lows, closes, volumes []float64) {
	// 成交量加权平均价格 (VWAP)
	vwap := helper.ChanToSlice(volume.Vwap(
		helper.SliceToChan(highs),
		helper.SliceToChan(lows),
		helper.SliceToChan(closes),
		helper.SliceToChan(volumes)))
	indicators["vwap"] = vwap

	// 能量潮指标 (OBV)
	obv := helper.ChanToSlice(volume.Obv(
		helper.SliceToChan(closes),
		helper.SliceToChan(volumes)))
	indicators["obv"] = obv

	// 资金流量指标 (MFI)
	mfi := helper.ChanToSlice(volume.Mfi(
		helper.SliceToChan(highs),
		helper.SliceToChan(lows),
		helper.SliceToChan(closes),
		helper.SliceToChan(volumes),
		14))
	indicators["mfi"] = mfi

	// 成交量相对强弱指标 (VRSI)
	vrsi := helper.ChanToSlice(volume.Vrsi(helper.SliceToChan(volumes), 14))
	indicators["vrsi"] = vrsi

	// 价格成交量趋势 (PVT)
	pvt := helper.ChanToSlice(volume.Pvt(
		helper.SliceToChan(closes),
		helper.SliceToChan(volumes)))
	indicators["pvt"] = pvt
}

// CalculateCustomIndicators 计算自定义指标
func (c *IndicatorCalculator) CalculateCustomIndicators(klines []models.KLineData) (map[string][]float64, error) {
	if len(klines) == 0 {
		return nil, fmt.Errorf("没有K线数据")
	}

	// 准备数据
	closes := make([]float64, len(klines))
	volumes := make([]float64, len(klines))

	for i, kline := range klines {
		closes[i] = kline.Close
		volumes[i] = kline.Volume
	}

	// 计算自定义指标
	indicators := make(map[string][]float64)

	// 价格变化率
	priceChangeRate := c.calculatePriceChangeRate(closes)
	indicators["price_change_rate"] = priceChangeRate

	// 价格动量
	priceMomentum := c.calculatePriceMomentum(closes)
	indicators["price_momentum"] = priceMomentum

	// 成交量变化率
	volumeChangeRate := c.calculateVolumeChangeRate(volumes)
	indicators["volume_change_rate"] = volumeChangeRate

	// 价格波动率
	priceVolatility := c.calculatePriceVolatility(closes, 14)
	indicators["price_volatility"] = priceVolatility

	return indicators, nil
}

// calculatePriceChangeRate 计算价格变化率
func (c *IndicatorCalculator) calculatePriceChangeRate(closes []float64) []float64 {
	result := make([]float64, len(closes))
	result[0] = 0

	for i := 1; i < len(closes); i++ {
		if closes[i-1] != 0 {
			result[i] = (closes[i] - closes[i-1]) / closes[i-1] * 100
		} else {
			result[i] = 0
		}
	}

	return result
}

// calculatePriceMomentum 计算价格动量
func (c *IndicatorCalculator) calculatePriceMomentum(closes []float64) []float64 {
	result := make([]float64, len(closes))
	for i := 0; i < len(closes); i++ {
		if i < 10 {
			result[i] = 0
		} else {
			result[i] = closes[i] - closes[i-10]
		}
	}

	return result
}

// calculateVolumeChangeRate 计算成交量变化率
func (c *IndicatorCalculator) calculateVolumeChangeRate(volumes []float64) []float64 {
	result := make([]float64, len(volumes))
	result[0] = 0

	for i := 1; i < len(volumes); i++ {
		if volumes[i-1] != 0 {
			result[i] = (volumes[i] - volumes[i-1]) / volumes[i-1] * 100
		} else {
			result[i] = 0
		}
	}

	return result
}

// calculatePriceVolatility 计算价格波动率
func (c *IndicatorCalculator) calculatePriceVolatility(closes []float64, period int) []float64 {
	result := make([]float64, len(closes))
	for i := 0; i < len(closes); i++ {
		if i < period {
			result[i] = 0
			continue
		}

		// 计算过去period天的收盘价标准差
		sum := 0.0
		for j := i - period + 1; j <= i; j++ {
			sum += closes[j]
		}
		mean := sum / float64(period)

		// 计算方差
		variance := 0.0
		for j := i - period + 1; j <= i; j++ {
			variance += (closes[j] - mean) * (closes[j] - mean)
		}
		variance /= float64(period)

		// 标准差
		result[i] = math.Sqrt(variance)
	}

	return result
}

