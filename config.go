package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// Config 表示应用程序配置
type Config struct {
	viper *viper.Viper
	log   *logrus.Logger
}

// New 创建一个新的配置实例
func New(log *logrus.Logger) *Config {
	return &Config{
		viper: viper.New(),
		log:   log,
	}
}

// LoadConfig 加载配置文件
func (c *Config) LoadConfig(configPath string) error {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		c.log.Warn("未找到.env文件，将使用环境变量")
	}

	// 设置配置文件路径
	c.viper.AddConfigPath(filepath.Dir(configPath))
	c.viper.SetConfigName(strings.TrimSuffix(filepath.Base(configPath), filepath.Ext(configPath)))
	c.viper.SetConfigType(strings.TrimPrefix(filepath.Ext(configPath), "."))

	// 读取配置文件
	if err := c.viper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 设置环境变量前缀
	c.viper.SetEnvPrefix("APP")
	c.viper.AutomaticEnv()
	c.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	return nil
}

// GetString 获取字符串配置值
func (c *Config) GetString(key string) string {
	return c.viper.GetString(key)
}

// GetInt 获取整数配置值
func (c *Config) GetInt(key string) int {
	return c.viper.GetInt(key)
}

// GetFloat64 获取浮点数配置值
func (c *Config) GetFloat64(key string) float64 {
	return c.viper.GetFloat64(key)
}

// GetBool 获取布尔配置值
func (c *Config) GetBool(key string) bool {
	return c.viper.GetBool(key)
}

// GetStringSlice 获取字符串切片配置值
func (c *Config) GetStringSlice(key string) []string {
	return c.viper.GetStringSlice(key)
}

// GetStringMap 获取字符串映射配置值
func (c *Config) GetStringMap(key string) map[string]interface{} {
	return c.viper.GetStringMap(key)
}

// GetStringMapString 获取字符串映射字符串配置值
func (c *Config) GetStringMapString(key string) map[string]string {
	return c.viper.GetStringMapString(key)
}

// Set 设置配置值
func (c *Config) Set(key string, value interface{}) {
	c.viper.Set(key, value)
}

// GetEnv 获取环境变量，如果不存在则返回默认值
func GetEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetConfigPath 获取配置文件路径
func GetConfigPath(configName string) string {
	// 首先检查当前目录
	if _, err := os.Stat(configName); err == nil {
		return configName
	}

	// 然后检查config目录
	configPath := filepath.Join("config", configName)
	if _, err := os.Stat(configPath); err == nil {
		return configPath
	}

	// 最后检查项目根目录下的config目录
	rootConfigPath := filepath.Join("..", "config", configName)
	if _, err := os.Stat(rootConfigPath); err == nil {
		return rootConfigPath
	}

	// 如果都找不到，返回默认路径
	return configPath
}

