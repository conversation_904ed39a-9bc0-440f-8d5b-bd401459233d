import React, { useState, useEffect } from 'react';
import { Card, Statistic, Row, Col, Tag, Spin, Progress, Typography } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, MinusOutlined } from '@ant-design/icons';
import { getLatestPrediction } from '../services/api';
import moment from 'moment';

const { Title, Text } = Typography;

const LatestPrediction = ({ symbol = 'BTCUSDT' }) => {
  const [loading, setLoading] = useState(true);
  const [prediction, setPrediction] = useState(null);
  const [timeLeft, setTimeLeft] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await getLatestPrediction(symbol);
        setPrediction(data);
      } catch (error) {
        console.error('获取最新预测失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    
    // 设置定时刷新
    const intervalId = setInterval(() => {
      fetchData();
    }, 60000); // 每分钟刷新一次
    
    return () => clearInterval(intervalId);
  }, [symbol]);

  useEffect(() => {
    if (!prediction || !prediction.target_time) return;
    
    const targetTime = moment(prediction.target_time);
    
    const updateTimeLeft = () => {
      const now = moment();
      if (now.isAfter(targetTime)) {
        setTimeLeft('已过期');
        return;
      }
      
      const duration = moment.duration(targetTime.diff(now));
      const minutes = Math.floor(duration.asMinutes());
      const seconds = Math.floor(duration.seconds());
      
      setTimeLeft(`${minutes}分${seconds}秒`);
    };
    
    updateTimeLeft();
    const timerId = setInterval(updateTimeLeft, 1000);
    
    return () => clearInterval(timerId);
  }, [prediction]);

  const getDirectionIcon = (direction) => {
    if (direction === 'up') {
      return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
    } else if (direction === 'down') {
      return <ArrowDownOutlined style={{ color: '#f5222d' }} />;
    } else {
      return <MinusOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getDirectionTag = (direction) => {
    if (direction === 'up') {
      return <Tag color="success">上涨</Tag>;
    } else if (direction === 'down') {
      return <Tag color="error">下跌</Tag>;
    } else {
      return <Tag color="processing">持平</Tag>;
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) {
      return '#52c41a';
    } else if (confidence >= 0.6) {
      return '#1890ff';
    } else {
      return '#faad14';
    }
  };

  return (
    <Card title="最新预测" bordered={false}>
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <Spin size="large" />
        </div>
      ) : prediction ? (
        <>
          <Row gutter={16}>
            <Col span={12}>
              <Statistic
                title="当前价格"
                value={prediction.current_price}
                precision={2}
                prefix="$"
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="预测方向"
                value={prediction.direction}
                prefix={getDirectionIcon(prediction.direction)}
                suffix={getDirectionTag(prediction.direction)}
                valueStyle={{ color: prediction.direction === 'up' ? '#52c41a' : prediction.direction === 'down' ? '#f5222d' : '#1890ff' }}
              />
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={12}>
              <Statistic
                title="预测变化"
                value={prediction.predicted_change}
                precision={2}
                suffix="%"
                valueStyle={{ color: prediction.predicted_change > 0 ? '#52c41a' : prediction.predicted_change < 0 ? '#f5222d' : '#1890ff' }}
              />
            </Col>
            <Col span={12}>
              <div>
                <Text>置信度</Text>
                <Progress
                  percent={Math.round(prediction.confidence * 100)}
                  status="active"
                  strokeColor={getConfidenceColor(prediction.confidence)}
                />
              </div>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={12}>
              <Statistic
                title="预测时间"
                value={moment(prediction.created_at).format('YYYY-MM-DD HH:mm:ss')}
                valueStyle={{ fontSize: '14px' }}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="目标时间"
                value={moment(prediction.target_time).format('YYYY-MM-DD HH:mm:ss')}
                valueStyle={{ fontSize: '14px' }}
              />
            </Col>
          </Row>
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <div style={{ textAlign: 'center' }}>
                <Title level={4}>距离目标时间还有</Title>
                <Title level={3} style={{ color: '#1890ff' }}>{timeLeft}</Title>
              </div>
            </Col>
          </Row>
        </>
      ) : (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          无预测数据
        </div>
      )}
    </Card>
  );
};

export default LatestPrediction;

