package models

import (
	"time"
)

// TrafficData 表示网站流量数据
type TrafficData struct {
	ID            uint      `gorm:"primaryKey" json:"-"`
	Target        string    `gorm:"index:idx_target_time" json:"target"` // 目标网站
	Timestamp     time.Time `gorm:"index:idx_target_time" json:"timestamp"`
	VisitorCount  int       `json:"visitor_count"`  // 访问人数
	PageViews     int       `json:"page_views"`     // 页面浏览量
	BounceRate    float64   `json:"bounce_rate"`    // 跳出率
	AvgTimeOnSite float64   `json:"avg_time_on_site"` // 平均停留时间（秒）
	NewVisitors   int       `json:"new_visitors"`   // 新访客数
	ReturnVisitors int      `json:"return_visitors"` // 回访访客数
	Source        string    `json:"source"`         // 数据来源
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 返回TrafficData的表名
func (TrafficData) TableName() string {
	return "traffic_data"
}

// SearchTrendData 表示搜索趋势数据
type SearchTrendData struct {
	ID           uint      `gorm:"primaryKey" json:"-"`
	Keyword      string    `gorm:"index:idx_keyword_time" json:"keyword"`
	Timestamp    time.Time `gorm:"index:idx_keyword_time" json:"timestamp"`
	SearchVolume int       `json:"search_volume"`  // 搜索量
	RelativeScore float64  `json:"relative_score"` // 相对分数（0-100）
	Region       string    `json:"region"`         // 地区
	Source       string    `json:"source"`         // 数据来源
	TimeWindow   string    `json:"time_window"`    // 时间窗口（1h, 4h, 1d等）
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 返回SearchTrendData的表名
func (SearchTrendData) TableName() string {
	return "search_trend_data"
}

// ReferralTraffic 表示引荐流量数据
type ReferralTraffic struct {
	ID           uint      `gorm:"primaryKey" json:"-"`
	Target       string    `gorm:"index:idx_target_time" json:"target"` // 目标网站
	Timestamp    time.Time `gorm:"index:idx_target_time" json:"timestamp"`
	ReferrerSite string    `json:"referrer_site"` // 引荐网站
	VisitorCount int       `json:"visitor_count"` // 访问人数
	PageViews    int       `json:"page_views"`    // 页面浏览量
	Source       string    `json:"source"`        // 数据来源
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 返回ReferralTraffic的表名
func (ReferralTraffic) TableName() string {
	return "referral_traffic"
}

// TrafficMetrics 表示流量指标数据
type TrafficMetrics struct {
	ID              uint      `gorm:"primaryKey" json:"-"`
	Symbol          string    `gorm:"index:idx_symbol_time" json:"symbol"`
	Timestamp       time.Time `gorm:"index:idx_symbol_time" json:"timestamp"`
	TotalTraffic    int       `json:"total_traffic"`    // 总流量
	TrafficChange   float64   `json:"traffic_change"`   // 流量变化百分比
	SearchVolume    int       `json:"search_volume"`    // 搜索量
	SearchChange    float64   `json:"search_change"`    // 搜索量变化百分比
	SocialMentions  int       `json:"social_mentions"`  // 社交媒体提及次数
	MentionsChange  float64   `json:"mentions_change"`  // 提及次数变化百分比
	OverallScore    float64   `json:"overall_score"`    // 综合得分
	TimeWindow      string    `json:"time_window"`      // 时间窗口（1h, 4h, 1d等）
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 返回TrafficMetrics的表名
func (TrafficMetrics) TableName() string {
	return "traffic_metrics"
}

