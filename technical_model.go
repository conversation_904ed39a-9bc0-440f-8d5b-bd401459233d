package prediction

import (
	"encoding/json"
	"fmt"
	"math"
	"os"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/analysis"
)

// TechnicalModel 表示基于技术分析的预测模型
type TechnicalModel struct {
	log        *logrus.Logger
	weights    map[string]float64 // 各指标权重
	thresholds map[string]struct {
		Buy  float64 // 买入阈值
		Sell float64 // 卖出阈值
	}
	trained bool
}

// NewTechnicalModel 创建一个新的技术分析模型
func NewTechnicalModel(log *logrus.Logger) *TechnicalModel {
	return &TechnicalModel{
		log:        log,
		weights:    make(map[string]float64),
		thresholds: make(map[string]struct{ Buy, Sell float64 }),
		trained:    false,
	}
}

// Name 返回模型名称
func (m *TechnicalModel) Name() string {
	return "technical"
}

// Predict 使用技术分析模型进行预测
func (m *TechnicalModel) Predict(features []analysis.Feature) (*PredictionResult, error) {
	if !m.trained {
		return nil, fmt.Errorf("模型尚未训练")
	}

	// 将特征转换为映射
	featureMap := analysis.FeaturesToMap(features)

	// 计算技术指标得分
	score := m.calculateTechnicalScore(featureMap)

	// 确定预测方向和变化百分比
	var direction Direction
	var predictedChange float64
	var confidence float64

	if score > 0.6 {
		direction = Up
		predictedChange = (score - 0.5) * 5 // 将0.6-1.0映射到0.5%-2.5%
		confidence = math.Min(1.0, score*1.2)
	} else if score < 0.4 {
		direction = Down
		predictedChange = (0.5 - score) * -5 // 将0.4-0.0映射到-0.5%至-2.5%
		confidence = math.Min(1.0, (1-score)*1.2)
	} else {
		direction = Neutral
		predictedChange = (score - 0.5) * 2 // 将0.4-0.6映射到-0.2%至0.2%
		confidence = 0.5 - math.Abs(score-0.5) // 越接近0.5，置信度越低
	}

	// 创建预测结果
	result := &PredictionResult{
		Direction:       direction,
		PredictedChange: predictedChange,
		Confidence:      confidence,
		ModelName:       m.Name(),
		Features:        featureMap,
	}

	return result, nil
}

// calculateTechnicalScore 计算技术指标得分
func (m *TechnicalModel) calculateTechnicalScore(features map[string]float64) float64 {
	// 初始化得分为0.5（中性）
	score := 0.5
	totalWeight := 0.0

	// 计算各指标的加权得分
	for name, weight := range m.weights {
		value, ok := features[name]
		if !ok {
			continue
		}

		// 根据阈值计算指标得分
		var indicatorScore float64
		if threshold, ok := m.thresholds[name]; ok {
			if value > threshold.Buy {
				// 超过买入阈值，看涨信号
				indicatorScore = 0.5 + math.Min(0.5, (value-threshold.Buy)/(threshold.Buy*2))
			} else if value < threshold.Sell {
				// 低于卖出阈值，看跌信号
				indicatorScore = 0.5 - math.Min(0.5, (threshold.Sell-value)/(threshold.Sell*2))
			} else {
				// 在阈值之间，中性信号
				range := threshold.Buy - threshold.Sell
				if range > 0 {
					position := (value - threshold.Sell) / range
					indicatorScore = 0.3 + position*0.4 // 映射到0.3-0.7
				} else {
					indicatorScore = 0.5
				}
			}
		} else {
			// 没有阈值，使用默认规则
			indicatorScore = m.getDefaultIndicatorScore(name, value)
		}

		// 累加加权得分
		score += (indicatorScore - 0.5) * weight
		totalWeight += math.Abs(weight)
	}

	// 归一化得分到0-1范围
	if totalWeight > 0 {
		score = 0.5 + (score - 0.5) / totalWeight
	}

	// 确保得分在0-1范围内
	return math.Max(0, math.Min(1, score))
}

// getDefaultIndicatorScore 获取指标的默认得分
func (m *TechnicalModel) getDefaultIndicatorScore(name string, value float64) float64 {
	switch name {
	case "rsi14":
		// RSI: 0-30看跌，30-70中性，70-100看涨
		if value > 70 {
			return 0.7 + math.Min(0.3, (value-70)/30)
		} else if value < 30 {
			return 0.3 - math.Min(0.3, (30-value)/30)
		} else {
			return 0.3 + (value-30)/40*0.4
		}
	case "macd_histogram":
		// MACD柱状图: 正值看涨，负值看跌
		if value > 0 {
			return 0.5 + math.Min(0.5, value/0.01)
		} else {
			return 0.5 - math.Min(0.5, -value/0.01)
		}
	case "stoch_k", "stoch_d":
		// 随机指标: 0-20看跌，20-80中性，80-100看涨
		if value > 80 {
			return 0.7 + math.Min(0.3, (value-80)/20)
		} else if value < 20 {
			return 0.3 - math.Min(0.3, (20-value)/20)
		} else {
			return 0.3 + (value-20)/60*0.4
		}
	case "bb_width":
		// 布林带宽度: 值越大，波动性越大，中性偏看涨
		return 0.5 + math.Min(0.3, value/0.05)
	case "percent_b":
		// %B: 0-0.2看跌，0.2-0.8中性，0.8-1看涨，>1极度看涨，<0极度看跌
		if value > 1 {
			return 0.9 + math.Min(0.1, (value-1)/0.5)
		} else if value > 0.8 {
			return 0.7 + (value-0.8)/0.2*0.2
		} else if value < 0 {
			return 0.1 - math.Min(0.1, -value/0.5)
		} else if value < 0.2 {
			return 0.3 - (0.2-value)/0.2*0.2
		} else {
			return 0.3 + (value-0.2)/0.6*0.4
		}
	case "atr":
		// ATR: 值越大，波动性越大，中性
		return 0.5
	case "mfi":
		// MFI: 类似RSI，0-20看跌，20-80中性，80-100看涨
		if value > 80 {
			return 0.7 + math.Min(0.3, (value-80)/20)
		} else if value < 20 {
			return 0.3 - math.Min(0.3, (20-value)/20)
		} else {
			return 0.3 + (value-20)/60*0.4
		}
	case "obv":
		// OBV: 相对变化更重要，单值意义不大，中性
		return 0.5
	case "price_change_percent":
		// 价格变化百分比: 正值看涨，负值看跌
		if value > 0 {
			return 0.5 + math.Min(0.4, value/5)
		} else {
			return 0.5 - math.Min(0.4, -value/5)
		}
	case "volume_change_percent":
		// 成交量变化百分比: 正值看涨，负值看跌，但影响较小
		if value > 0 {
			return 0.5 + math.Min(0.2, value/20)
		} else {
			return 0.5 - math.Min(0.2, -value/20)
		}
	case "macd_cross_signal":
		// MACD交叉信号: 1看涨，-1看跌，0中性
		if value > 0 {
			return 0.8
		} else if value < 0 {
			return 0.2
		} else {
			return 0.5
		}
	case "ema5_cross_ema10", "ema10_cross_ema20", "ema20_cross_ema50", "ema50_cross_ema200":
		// EMA交叉信号: 1看涨，-1看跌，0中性
		if value > 0 {
			return 0.8
		} else if value < 0 {
			return 0.2
		} else {
			return 0.5
		}
	case "hammer_pattern":
		// 锤子线形态: 1或2看涨，0中性
		if value > 0 {
			return 0.7 + value*0.1
		} else {
			return 0.5
		}
	case "engulfing_pattern":
		// 吞没形态: 1看涨，-1看跌，0中性
		if value > 0 {
			return 0.8
		} else if value < 0 {
			return 0.2
		} else {
			return 0.5
		}
	case "morning_star":
		// 晨星形态: 1看涨，0中性
		if value > 0 {
			return 0.9
		} else {
			return 0.5
		}
	case "evening_star":
		// 黄昏星形态: 1看跌，0中性
		if value > 0 {
			return 0.1
		} else {
			return 0.5
		}
	default:
		// 其他指标，默认中性
		return 0.5
	}
}

// Train 训练技术分析模型
func (m *TechnicalModel) Train(features [][]analysis.Feature, labels []float64) error {
	if len(features) == 0 || len(labels) == 0 {
		return fmt.Errorf("训练数据为空")
	}

	if len(features) != len(labels) {
		return fmt.Errorf("特征和标签数量不匹配")
	}

	// 初始化权重
	m.initializeWeights()

	// 初始化阈值
	m.initializeThresholds()

	// 标记为已训练
	m.trained = true

	return nil
}

// initializeWeights 初始化权重
func (m *TechnicalModel) initializeWeights() {
	// 设置各指标的权重
	m.weights = map[string]float64{
		// 趋势指标
		"macd_histogram":      0.15,
		"macd_cross_signal":   0.20,
		"ema5_cross_ema10":    0.05,
		"ema10_cross_ema20":   0.10,
		"ema20_cross_ema50":   0.15,
		"ema50_cross_ema200":  0.20,

		// 动量指标
		"rsi14":              0.15,
		"stoch_k":            0.10,
		"stoch_d":            0.10,
		"williams_r":         0.05,
		"tsi":                0.05,

		// 波动性指标
		"bb_width":           0.05,
		"percent_b":          0.15,
		"atr":                0.05,

		// 成交量指标
		"mfi":                0.10,
		"obv":                0.05,
		"volume_change_percent": 0.05,

		// 价格模式
		"hammer_pattern":     0.10,
		"engulfing_pattern":  0.15,
		"morning_star":       0.20,
		"evening_star":       0.20,

		// 价格变化
		"price_change_percent": 0.10,
	}
}

// initializeThresholds 初始化阈值
func (m *TechnicalModel) initializeThresholds() {
	// 设置各指标的阈值
	m.thresholds = map[string]struct{ Buy, Sell float64 }{
		"rsi14":     {70, 30},
		"stoch_k":   {80, 20},
		"stoch_d":   {80, 20},
		"williams_r": {-20, -80}, // 注意Williams %R是反向的
		"mfi":       {80, 20},
		"percent_b": {0.8, 0.2},
	}
}

// Save 保存模型
func (m *TechnicalModel) Save(path string) error {
	// 创建模型数据结构
	data := struct {
		Weights    map[string]float64
		Thresholds map[string]struct{ Buy, Sell float64 }
		Trained    bool
	}{
		Weights:    m.weights,
		Thresholds: m.thresholds,
		Trained:    m.trained,
	}

	// 序列化为JSON
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化模型失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(path, jsonData, 0644); err != nil {
		return fmt.Errorf("写入模型文件失败: %w", err)
	}

	return nil
}

// Load 加载模型
func (m *TechnicalModel) Load(path string) error {
	// 读取文件
	jsonData, err := os.ReadFile(path)
	if err != nil {
		// 如果文件不存在，初始化一个新模型
		if os.IsNotExist(err) {
			m.initializeWeights()
			m.initializeThresholds()
			m.trained = true
			return nil
		}
		return fmt.Errorf("读取模型文件失败: %w", err)
	}

	// 反序列化JSON
	data := struct {
		Weights    map[string]float64
		Thresholds map[string]struct{ Buy, Sell float64 }
		Trained    bool
	}{}
	if err := json.Unmarshal(jsonData, &data); err != nil {
		return fmt.Errorf("反序列化模型失败: %w", err)
	}

	// 更新模型参数
	m.weights = data.Weights
	m.thresholds = data.Thresholds
	m.trained = data.Trained

	return nil
}

