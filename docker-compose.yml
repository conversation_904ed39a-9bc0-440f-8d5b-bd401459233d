version: '3.8'

services:
  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: btc-predictor
    restart: unless-stopped
    depends_on:
      - mysql
      - redis
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=btcpredictor
      - DB_PASSWORD=password
      - DB_NAME=btcpredictor
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_API_SECRET=${BINANCE_API_SECRET}
      - BINANCE_USE_TESTNET=${BINANCE_USE_TESTNET:-true}
    volumes:
      - ./config:/app/config
      - ./data:/app/data
    ports:
      - "8080:8080"
    networks:
      - btc-predictor-network
    command: ["-mode", "collect", "-symbol", "BTCUSDT", "-interval", "1m"]

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: btc-predictor-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=btcpredictor
      - MYSQL_USER=btcpredictor
      - MYSQL_PASSWORD=password
    volumes:
      - mysql-data:/var/lib/mysql
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "3306:3306"
    networks:
      - btc-predictor-network
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis缓存
  redis:
    image: redis:6-alpine
    container_name: btc-predictor-redis
    restart: unless-stopped
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - btc-predictor-network
    command: redis-server --appendonly yes

  # 数据采集服务
  collector:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: btc-predictor-collector
    restart: unless-stopped
    depends_on:
      - mysql
      - redis
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=btcpredictor
      - DB_PASSWORD=password
      - DB_NAME=btcpredictor
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_API_SECRET=${BINANCE_API_SECRET}
      - BINANCE_USE_TESTNET=${BINANCE_USE_TESTNET:-true}
    volumes:
      - ./config:/app/config
      - ./data:/app/data
    networks:
      - btc-predictor-network
    command: ["-mode", "collect", "-symbol", "BTCUSDT", "-interval", "1m"]

  # 预测服务
  predictor:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: btc-predictor-predictor
    restart: unless-stopped
    depends_on:
      - mysql
      - redis
      - collector
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=btcpredictor
      - DB_PASSWORD=password
      - DB_NAME=btcpredictor
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_API_SECRET=${BINANCE_API_SECRET}
      - BINANCE_USE_TESTNET=${BINANCE_USE_TESTNET:-true}
    volumes:
      - ./config:/app/config
      - ./data:/app/data
    networks:
      - btc-predictor-network
    command: ["-mode", "predict", "-symbol", "BTCUSDT", "-interval", "10m"]

volumes:
  mysql-data:
  redis-data:

networks:
  btc-predictor-network:
    driver: bridge

