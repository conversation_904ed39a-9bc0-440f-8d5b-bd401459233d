# 使用多阶段构建
# 构建阶段
FROM golang:1.20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache git make

# 复制go.mod和go.sum文件
COPY go.mod ./
COPY go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN make build

# 运行阶段
FROM alpine:latest

# 设置工作目录
WORKDIR /app

# 安装运行时依赖
RUN apk add --no-cache ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 从构建阶段复制二进制文件
COPY --from=builder /app/bin/btc-predictor /app/bin/btc-predictor

# 复制配置文件
COPY --from=builder /app/config /app/config

# 创建数据目录
RUN mkdir -p /app/data

# 设置环境变量
ENV PATH="/app/bin:${PATH}"

# 暴露端口
EXPOSE 8080

# 设置入口点
ENTRYPOINT ["/app/bin/btc-predictor"]

# 设置默认命令
CMD ["-config", "/app/config/config.yaml"]

