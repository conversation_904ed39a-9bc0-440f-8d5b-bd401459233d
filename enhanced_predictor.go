package prediction

import (
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/analysis"
	"github.com/user/btc-predictor/internal/models"
)

// EnhancedPredictor 表示增强的预测器
type EnhancedPredictor struct {
	log                *logrus.Logger
	modelManager       *ModelManager
	indicatorCalculator *analysis.IndicatorCalculator
	featureExtractor   *analysis.FeatureExtractor
	sentimentAnalyzer  *analysis.SentimentAnalyzer
	trafficAnalyzer    *analysis.TrafficAnalyzer
	modelWeights       map[string]float64
	adaptiveWeighting  bool
	ensembleMethod     string
	confidenceThreshold float64
	performanceHistory map[string][]PredictionPerformance
	cacheEnabled       bool
	predictionCache    map[string]*PredictionResult
	cacheTTL           time.Duration
}

// PredictionPerformance 表示预测性能
type PredictionPerformance struct {
	ModelName    string
	IsCorrect    bool
	Confidence   float64
	ActualChange float64
	Timestamp    time.Time
}

// NewEnhancedPredictor 创建一个新的增强预测器
func NewEnhancedPredictor(
	log *logrus.Logger,
	modelManager *ModelManager,
	indicatorCalculator *analysis.IndicatorCalculator,
	featureExtractor *analysis.FeatureExtractor,
	sentimentAnalyzer *analysis.SentimentAnalyzer,
	trafficAnalyzer *analysis.TrafficAnalyzer,
) *EnhancedPredictor {
	return &EnhancedPredictor{
		log:                log,
		modelManager:       modelManager,
		indicatorCalculator: indicatorCalculator,
		featureExtractor:   featureExtractor,
		sentimentAnalyzer:  sentimentAnalyzer,
		trafficAnalyzer:    trafficAnalyzer,
		modelWeights: map[string]float64{
			"enhanced_model":  0.5,
			"technical_model": 0.3,
			"ensemble_model":  0.2,
		},
		adaptiveWeighting:   true,
		ensembleMethod:      "weighted_average",
		confidenceThreshold: 0.65,
		performanceHistory:  make(map[string][]PredictionPerformance),
		cacheEnabled:        true,
		predictionCache:     make(map[string]*PredictionResult),
		cacheTTL:            5 * time.Minute,
	}
}

// PredictPrice 预测价格
func (p *EnhancedPredictor) PredictPrice(
	klines []models.KLineData,
	sentiments []models.SentimentData,
	influencerPosts []models.InfluencerPost,
	trafficData []models.TrafficData,
	searchTrends []models.SearchTrendData,
	referralTraffic []models.ReferralTraffic,
	targetTimeMinutes int,
) (*models.Prediction, error) {
	// 检查数据是否足够
	if len(klines) < 100 {
		return nil, fmt.Errorf("数据点数量不足，需要至少100个，实际%d个", len(klines))
	}

	// 生成缓存键
	cacheKey := generateCacheKey(klines[len(klines)-1], targetTimeMinutes)

	// 检查缓存
	if p.cacheEnabled {
		if cachedResult, ok := p.predictionCache[cacheKey]; ok {
			// 检查缓存是否过期
			if time.Since(cachedResult.Timestamp) < p.cacheTTL {
				p.log.Debug("使用缓存的预测结果")
				return p.createPredictionFromResult(cachedResult, klines[len(klines)-1], targetTimeMinutes), nil
			}
			// 缓存过期，删除
			delete(p.predictionCache, cacheKey)
		}
	}

	// 计算技术指标
	indicators, err := p.indicatorCalculator.CalculateIndicators(klines)
	if err != nil {
		return nil, fmt.Errorf("计算技术指标失败: %w", err)
	}

	// 计算自定义指标
	customIndicators, err := p.indicatorCalculator.CalculateCustomIndicators(klines)
	if err != nil {
		return nil, fmt.Errorf("计算自定义指标失败: %w", err)
	}

	// 合并所有指标
	for name, values := range customIndicators {
		indicators[name] = values
	}

	// 提取特征
	features, err := p.featureExtractor.ExtractFeatures(klines, indicators, len(klines)-1)
	if err != nil {
		return nil, fmt.Errorf("提取特征失败: %w", err)
	}

	// 分析舆情数据
	if len(sentiments) > 0 || len(influencerPosts) > 0 {
		sentimentFeatures, err := p.sentimentAnalyzer.AnalyzeSentiment(sentiments, influencerPosts)
		if err != nil {
			p.log.WithError(err).Warn("分析舆情数据失败")
		} else {
			// 添加舆情特征
			features = append(features, sentimentFeatures...)
		}
	}

	// 分析流量数据
	if len(trafficData) > 0 || len(searchTrends) > 0 || len(referralTraffic) > 0 {
		trafficFeatures, err := p.trafficAnalyzer.AnalyzeTraffic(trafficData, searchTrends, referralTraffic)
		if err != nil {
			p.log.WithError(err).Warn("分析流量数据失败")
		} else {
			// 添加流量特征
			features = append(features, trafficFeatures...)
		}
	}

	// 获取所有模型
	models := p.modelManager.GetModels()

	// 使用每个模型进行预测
	predictions := make([]*PredictionResult, 0, len(models))
	for _, model := range models {
		result, err := model.Predict(
			klines,
			indicators,
			features,
			sentiments,
			influencerPosts,
			trafficData,
			searchTrends,
			referralTraffic,
			targetTimeMinutes,
		)
		if err != nil {
			p.log.WithError(err).WithField("model", model.Name()).Warn("模型预测失败")
			continue
		}

		predictions = append(predictions, result)
	}

	// 如果没有有效的预测结果，返回错误
	if len(predictions) == 0 {
		return nil, fmt.Errorf("没有有效的预测结果")
	}

	// 整合预测结果
	result, err := p.ensemblePredictions(predictions)
	if err != nil {
		return nil, fmt.Errorf("整合预测结果失败: %w", err)
	}

	// 添加时间戳
	result.Timestamp = time.Now()

	// 缓存预测结果
	if p.cacheEnabled {
		p.predictionCache[cacheKey] = result
	}

	// 创建预测对象
	prediction := p.createPredictionFromResult(result, klines[len(klines)-1], targetTimeMinutes)

	return prediction, nil
}

// ensemblePredictions 整合多个预测结果
func (p *EnhancedPredictor) ensemblePredictions(predictions []*PredictionResult) (*PredictionResult, error) {
	if len(predictions) == 0 {
		return nil, fmt.Errorf("没有预测结果可整合")
	}

	// 如果只有一个预测结果，直接返回
	if len(predictions) == 1 {
		return predictions[0], nil
	}

	// 根据整合方法选择不同的整合策略
	switch p.ensembleMethod {
	case "weighted_average":
		return p.weightedAverageEnsemble(predictions)
	case "confidence_weighted":
		return p.confidenceWeightedEnsemble(predictions)
	case "performance_weighted":
		return p.performanceWeightedEnsemble(predictions)
	case "majority_vote":
		return p.majorityVoteEnsemble(predictions)
	default:
		return p.weightedAverageEnsemble(predictions)
	}
}

// weightedAverageEnsemble 加权平均整合
func (p *EnhancedPredictor) weightedAverageEnsemble(predictions []*PredictionResult) (*PredictionResult, error) {
	// 初始化权重总和和加权得分
	totalWeight := 0.0
	weightedDirectionScore := 0.0
	weightedChangeScore := 0.0
	weightedConfidence := 0.0
	
	// 计算每个模型的加权得分
	for _, pred := range predictions {
		// 获取模型权重
		weight := p.getModelWeight(pred.ModelName)
		
		// 将方向转换为数值
		directionScore := 0.0
		switch pred.Direction {
		case Up:
			directionScore = 1.0
		case Down:
			directionScore = -1.0
		case Neutral:
			directionScore = 0.0
		}
		
		// 计算加权得分
		weightedDirectionScore += directionScore * weight
		weightedChangeScore += pred.PredictedChange * weight
		weightedConfidence += pred.Confidence * weight
		totalWeight += weight
	}
	
	// 归一化得分
	if totalWeight > 0 {
		weightedDirectionScore /= totalWeight
		weightedChangeScore /= totalWeight
		weightedConfidence /= totalWeight
	}
	
	// 确定最终方向
	var finalDirection Direction
	if weightedDirectionScore > 0.2 {
		finalDirection = Up
	} else if weightedDirectionScore < -0.2 {
		finalDirection = Down
	} else {
		finalDirection = Neutral
	}
	
	// 如果置信度不够，返回中性预测
	if weightedConfidence < p.confidenceThreshold {
		p.log.WithFields(logrus.Fields{
			"weighted_confidence": weightedConfidence,
			"threshold":           p.confidenceThreshold,
		}).Debug("整合后置信度不足，返回中性预测")
		
		finalDirection = Neutral
		weightedChangeScore = 0.0
		weightedConfidence = 0.5
	}
	
	// 创建整合后的预测结果
	result := &PredictionResult{
		Direction:       finalDirection,
		PredictedChange: weightedChangeScore,
		Confidence:      weightedConfidence,
		ModelName:       "ensemble",
		Features:        predictions[0].Features, // 使用第一个预测的特征
		Scores:          make(map[string]float64),
	}
	
	// 添加每个模型的得分
	for _, pred := range predictions {
		result.Scores[pred.ModelName+"_direction"] = float64(pred.Direction)
		result.Scores[pred.ModelName+"_change"] = pred.PredictedChange
		result.Scores[pred.ModelName+"_confidence"] = pred.Confidence
	}
	
	return result, nil
}

// confidenceWeightedEnsemble 置信度加权整合
func (p *EnhancedPredictor) confidenceWeightedEnsemble(predictions []*PredictionResult) (*PredictionResult, error) {
	// 初始化权重总和和加权得分
	totalWeight := 0.0
	weightedDirectionScore := 0.0
	weightedChangeScore := 0.0
	weightedConfidence := 0.0
	
	// 计算每个模型的加权得分
	for _, pred := range predictions {
		// 使用置信度作为权重
		weight := pred.Confidence
		
		// 将方向转换为数值
		directionScore := 0.0
		switch pred.Direction {
		case Up:
			directionScore = 1.0
		case Down:
			directionScore = -1.0
		case Neutral:
			directionScore = 0.0
		}
		
		// 计算加权得分
		weightedDirectionScore += directionScore * weight
		weightedChangeScore += pred.PredictedChange * weight
		weightedConfidence += pred.Confidence * weight
		totalWeight += weight
	}
	
	// 归一化得分
	if totalWeight > 0 {
		weightedDirectionScore /= totalWeight
		weightedChangeScore /= totalWeight
		weightedConfidence /= totalWeight
	}
	
	// 确定最终方向
	var finalDirection Direction
	if weightedDirectionScore > 0.2 {
		finalDirection = Up
	} else if weightedDirectionScore < -0.2 {
		finalDirection = Down
	} else {
		finalDirection = Neutral
	}
	
	// 如果置信度不够，返回中性预测
	if weightedConfidence < p.confidenceThreshold {
		p.log.WithFields(logrus.Fields{
			"weighted_confidence": weightedConfidence,
			"threshold":           p.confidenceThreshold,
		}).Debug("整合后置信度不足，返回中性预测")
		
		finalDirection = Neutral
		weightedChangeScore = 0.0
		weightedConfidence = 0.5
	}
	
	// 创建整合后的预测结果
	result := &PredictionResult{
		Direction:       finalDirection,
		PredictedChange: weightedChangeScore,
		Confidence:      weightedConfidence,
		ModelName:       "ensemble",
		Features:        predictions[0].Features, // 使用第一个预测的特征
		Scores:          make(map[string]float64),
	}
	
	// 添加每个模型的得分
	for _, pred := range predictions {
		result.Scores[pred.ModelName+"_direction"] = float64(pred.Direction)
		result.Scores[pred.ModelName+"_change"] = pred.PredictedChange
		result.Scores[pred.ModelName+"_confidence"] = pred.Confidence
	}
	
	return result, nil
}

// performanceWeightedEnsemble 性能加权整合
func (p *EnhancedPredictor) performanceWeightedEnsemble(predictions []*PredictionResult) (*PredictionResult, error) {
	// 初始化权重总和和加权得分
	totalWeight := 0.0
	weightedDirectionScore := 0.0
	weightedChangeScore := 0.0
	weightedConfidence := 0.0
	
	// 计算每个模型的加权得分
	for _, pred := range predictions {
		// 获取模型性能权重
		weight := p.getModelPerformanceWeight(pred.ModelName)
		
		// 将方向转换为数值
		directionScore := 0.0
		switch pred.Direction {
		case Up:
			directionScore = 1.0
		case Down:
			directionScore = -1.0
		case Neutral:
			directionScore = 0.0
		}
		
		// 计算加权得分
		weightedDirectionScore += directionScore * weight
		weightedChangeScore += pred.PredictedChange * weight
		weightedConfidence += pred.Confidence * weight
		totalWeight += weight
	}
	
	// 归一化得分
	if totalWeight > 0 {
		weightedDirectionScore /= totalWeight
		weightedChangeScore /= totalWeight
		weightedConfidence /= totalWeight
	}
	
	// 确定最终方向
	var finalDirection Direction
	if weightedDirectionScore > 0.2 {
		finalDirection = Up
	} else if weightedDirectionScore < -0.2 {
		finalDirection = Down
	} else {
		finalDirection = Neutral
	}
	
	// 如果置信度不够，返回中性预测
	if weightedConfidence < p.confidenceThreshold {
		p.log.WithFields(logrus.Fields{
			"weighted_confidence": weightedConfidence,
			"threshold":           p.confidenceThreshold,
		}).Debug("整合后置信度不足，返回中性预测")
		
		finalDirection = Neutral
		weightedChangeScore = 0.0
		weightedConfidence = 0.5
	}
	
	// 创建整合后的预测结果
	result := &PredictionResult{
		Direction:       finalDirection,
		PredictedChange: weightedChangeScore,
		Confidence:      weightedConfidence,
		ModelName:       "ensemble",
		Features:        predictions[0].Features, // 使用第一个预测的特征
		Scores:          make(map[string]float64),
	}
	
	// 添加每个模型的得分
	for _, pred := range predictions {
		result.Scores[pred.ModelName+"_direction"] = float64(pred.Direction)
		result.Scores[pred.ModelName+"_change"] = pred.PredictedChange
		result.Scores[pred.ModelName+"_confidence"] = pred.Confidence
	}
	
	return result, nil
}

// majorityVoteEnsemble 多数投票整合
func (p *EnhancedPredictor) majorityVoteEnsemble(predictions []*PredictionResult) (*PredictionResult, error) {
	// 统计每个方向的票数
	upVotes := 0
	downVotes := 0
	neutralVotes := 0
	
	// 计算总变化和总置信度
	totalChange := 0.0
	totalConfidence := 0.0
	
	// 统计票数
	for _, pred := range predictions {
		switch pred.Direction {
		case Up:
			upVotes++
		case Down:
			downVotes++
		case Neutral:
			neutralVotes++
		}
		
		totalChange += pred.PredictedChange
		totalConfidence += pred.Confidence
	}
	
	// 确定最终方向
	var finalDirection Direction
	if upVotes > downVotes && upVotes > neutralVotes {
		finalDirection = Up
	} else if downVotes > upVotes && downVotes > neutralVotes {
		finalDirection = Down
	} else {
		finalDirection = Neutral
	}
	
	// 计算平均变化和平均置信度
	avgChange := totalChange / float64(len(predictions))
	avgConfidence := totalConfidence / float64(len(predictions))
	
	// 如果置信度不够，返回中性预测
	if avgConfidence < p.confidenceThreshold {
		p.log.WithFields(logrus.Fields{
			"avg_confidence": avgConfidence,
			"threshold":      p.confidenceThreshold,
		}).Debug("整合后置信度不足，返回中性预测")
		
		finalDirection = Neutral
		avgChange = 0.0
		avgConfidence = 0.5
	}
	
	// 创建整合后的预测结果
	result := &PredictionResult{
		Direction:       finalDirection,
		PredictedChange: avgChange,
		Confidence:      avgConfidence,
		ModelName:       "ensemble",
		Features:        predictions[0].Features, // 使用第一个预测的特征
		Scores:          make(map[string]float64),
	}
	
	// 添加投票统计
	result.Scores["up_votes"] = float64(upVotes)
	result.Scores["down_votes"] = float64(downVotes)
	result.Scores["neutral_votes"] = float64(neutralVotes)
	
	// 添加每个模型的得分
	for _, pred := range predictions {
		result.Scores[pred.ModelName+"_direction"] = float64(pred.Direction)
		result.Scores[pred.ModelName+"_change"] = pred.PredictedChange
		result.Scores[pred.ModelName+"_confidence"] = pred.Confidence
	}
	
	return result, nil
}

// getModelWeight 获取模型权重
func (p *EnhancedPredictor) getModelWeight(modelName string) float64 {
	// 如果存在预定义权重，使用预定义权重
	if weight, ok := p.modelWeights[modelName]; ok {
		return weight
	}
	
	// 否则返回默认权重
	return 0.5
}

// getModelPerformanceWeight 获取模型性能权重
func (p *EnhancedPredictor) getModelPerformanceWeight(modelName string) float64 {
	// 如果没有性能历史记录，使用预定义权重
	performances, ok := p.performanceHistory[modelName]
	if !ok || len(performances) == 0 {
		return p.getModelWeight(modelName)
	}
	
	// 计算最近的性能
	correctCount := 0
	totalCount := 0
	
	// 只考虑最近的50条记录
	recentLimit := 50
	if len(performances) > recentLimit {
		performances = performances[len(performances)-recentLimit:]
	}
	
	for _, perf := range performances {
		if perf.IsCorrect {
			correctCount++
		}
		totalCount++
	}
	
	// 计算准确率
	accuracy := float64(correctCount) / float64(totalCount)
	
	// 将准确率映射到权重范围 [0.1, 1.0]
	weight := 0.1 + 0.9*accuracy
	
	return weight
}

// createPredictionFromResult 从预测结果创建预测对象
func (p *EnhancedPredictor) createPredictionFromResult(result *PredictionResult, lastKLine models.KLineData, targetTimeMinutes int) *models.Prediction {
	// 创建预测对象
	prediction := &models.Prediction{
		Symbol:          lastKLine.Symbol,
		Interval:        lastKLine.Interval,
		CurrentPrice:    lastKLine.Close,
		Direction:       string(result.Direction),
		PredictedChange: result.PredictedChange,
		Confidence:      result.Confidence,
		CreatedAt:       time.Now(),
		TargetTime:      time.Now().Add(time.Duration(targetTimeMinutes) * time.Minute),
		ModelName:       result.ModelName,
		Features:        make(map[string]float64),
	}
	
	// 复制特征
	for name, value := range result.Features {
		prediction.Features[name] = value
	}
	
	// 复制得分
	for name, value := range result.Scores {
		prediction.Features["score_"+name] = value
	}
	
	return prediction
}

// EvaluatePrediction 评估预测结果
func (p *EnhancedPredictor) EvaluatePrediction(prediction *models.Prediction, actualPrice float64) {
	// 计算实际变化
	actualChange := (actualPrice - prediction.CurrentPrice) / prediction.CurrentPrice * 100
	
	// 更新预测对象
	prediction.ActualPrice = actualPrice
	prediction.ActualChange = actualChange
	prediction.EvaluatedAt = time.Now()
	
	// 判断预测是否正确
	isCorrect := false
	switch prediction.Direction {
	case "up":
		isCorrect = actualChange > 0
	case "down":
		isCorrect = actualChange < 0
	case "neutral":
		isCorrect = math.Abs(actualChange) < 0.5 // 变化小于0.5%视为中性
	}
	
	prediction.IsCorrect = isCorrect
	
	// 记录性能
	performance := PredictionPerformance{
		ModelName:    prediction.ModelName,
		IsCorrect:    isCorrect,
		Confidence:   prediction.Confidence,
		ActualChange: actualChange,
		Timestamp:    time.Now(),
	}
	
	// 添加到性能历史记录
	p.performanceHistory[prediction.ModelName] = append(p.performanceHistory[prediction.ModelName], performance)
	
	// 如果启用了自适应权重，更新模型权重
	if p.adaptiveWeighting {
		p.updateModelWeights()
	}
	
	p.log.WithFields(logrus.Fields{
		"model":          prediction.ModelName,
		"direction":      prediction.Direction,
		"predicted_change": prediction.PredictedChange,
		"actual_change":  actualChange,
		"is_correct":     isCorrect,
	}).Info("评估预测结果")
}

// updateModelWeights 更新模型权重
func (p *EnhancedPredictor) updateModelWeights() {
	// 计算每个模型的准确率
	accuracies := make(map[string]float64)
	
	for modelName, performances := range p.performanceHistory {
		// 只考虑最近的50条记录
		recentLimit := 50
		if len(performances) > recentLimit {
			performances = performances[len(performances)-recentLimit:]
		}
		
		// 计算准确率
		correctCount := 0
		totalCount := 0
		
		for _, perf := range performances {
			if perf.IsCorrect {
				correctCount++
			}
			totalCount++
		}
		
		if totalCount > 0 {
			accuracies[modelName] = float64(correctCount) / float64(totalCount)
		} else {
			accuracies[modelName] = 0.5 // 默认准确率
		}
	}
	
	// 计算总准确率
	totalAccuracy := 0.0
	for _, accuracy := range accuracies {
		totalAccuracy += accuracy
	}
	
	// 更新权重
	if totalAccuracy > 0 {
		for modelName, accuracy := range accuracies {
			// 将准确率映射到权重范围 [0.1, 1.0]
			p.modelWeights[modelName] = 0.1 + 0.9*accuracy/totalAccuracy
		}
	}
	
	p.log.WithField("weights", p.modelWeights).Debug("更新模型权重")
}

// generateCacheKey 生成缓存键
func generateCacheKey(kline models.KLineData, targetTimeMinutes int) string {
	return fmt.Sprintf("%s_%s_%d_%d_%d",
		kline.Symbol,
		kline.Interval,
		kline.OpenTime.Unix(),
		kline.Close,
		targetTimeMinutes,
	)
}

