package prediction

import (
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/analysis"
	"github.com/user/btc-predictor/internal/models"
)

// Predictor 表示预测器
type Predictor struct {
	log                *logrus.Logger
	modelManager       *ModelManager
	indicatorCalculator *analysis.IndicatorCalculator
	featureExtractor   *analysis.FeatureExtractor
	sentimentAnalyzer  *analysis.SentimentAnalyzer
	trafficAnalyzer    *analysis.TrafficAnalyzer
}

// NewPredictor 创建一个新的预测器
func NewPredictor(
	log *logrus.Logger,
	modelManager *ModelManager,
	indicatorCalculator *analysis.IndicatorCalculator,
	featureExtractor *analysis.FeatureExtractor,
	sentimentAnalyzer *analysis.SentimentAnalyzer,
	trafficAnalyzer *analysis.TrafficAnalyzer,
) *Predictor {
	return &Predictor{
		log:                log,
		modelManager:       modelManager,
		indicatorCalculator: indicatorCalculator,
		featureExtractor:   featureExtractor,
		sentimentAnalyzer:  sentimentAnalyzer,
		trafficAnalyzer:    trafficAnalyzer,
	}
}

// PredictPrice 预测价格走势
func (p *Predictor) PredictPrice(
	klines []models.KLineData,
	sentiments []models.SentimentData,
	influencerPosts []models.InfluencerPost,
	trafficData []models.TrafficData,
	searchTrendData []models.SearchTrendData,
	referralTraffic []models.ReferralTraffic,
	targetTimeMinutes int,
) (*models.PredictionData, error) {
	if len(klines) == 0 {
		return nil, fmt.Errorf("没有K线数据")
	}

	// 获取最新的K线数据
	latestKline := klines[len(klines)-1]
	symbol := latestKline.Symbol
	predictionTime := time.Now()
	targetTime := predictionTime.Add(time.Duration(targetTimeMinutes) * time.Minute)

	// 计算技术指标
	indicators, err := p.indicatorCalculator.CalculateIndicators(klines)
	if err != nil {
		return nil, fmt.Errorf("计算技术指标失败: %w", err)
	}

	// 计算自定义指标
	customIndicators, err := p.indicatorCalculator.CalculateCustomIndicators(klines)
	if err != nil {
		return nil, fmt.Errorf("计算自定义指标失败: %w", err)
	}

	// 合并所有指标
	for name, values := range customIndicators {
		indicators[name] = values
	}

	// 提取特征
	features, err := p.featureExtractor.ExtractFeatures(klines, indicators, len(klines)-1)
	if err != nil {
		return nil, fmt.Errorf("提取特征失败: %w", err)
	}

	// 分析舆情数据
	var sentimentScore *analysis.SentimentScore
	if len(sentiments) > 0 || len(influencerPosts) > 0 {
		sentimentScore, err = p.sentimentAnalyzer.AnalyzeSentiment(
			sentiments,
			influencerPosts,
			predictionTime,
			24*time.Hour, // 使用过去24小时的舆情数据
		)
		if err != nil {
			p.log.WithError(err).Warn("分析舆情数据失败")
		}
	}

	// 分析流量数据
	var trafficScore *analysis.TrafficScore
	if len(trafficData) > 0 || len(searchTrendData) > 0 || len(referralTraffic) > 0 {
		trafficScore, err = p.trafficAnalyzer.AnalyzeTraffic(
			trafficData,
			searchTrendData,
			referralTraffic,
			predictionTime,
			24*time.Hour, // 使用过去24小时的流量数据
		)
		if err != nil {
			p.log.WithError(err).Warn("分析流量数据失败")
		}
	}

	// 添加舆情特征
	if sentimentScore != nil {
		features = append(features, analysis.Feature{Name: "sentiment_overall_score", Value: sentimentScore.OverallScore})
		features = append(features, analysis.Feature{Name: "sentiment_positive_count", Value: float64(sentimentScore.PositiveCount)})
		features = append(features, analysis.Feature{Name: "sentiment_negative_count", Value: float64(sentimentScore.NegativeCount)})
		features = append(features, analysis.Feature{Name: "sentiment_neutral_count", Value: float64(sentimentScore.NeutralCount)})
		features = append(features, analysis.Feature{Name: "sentiment_recent_trend", Value: sentimentScore.RecentTrend})
		features = append(features, analysis.Feature{Name: "sentiment_confidence", Value: sentimentScore.ConfidenceScore})
	}

	// 添加流量特征
	if trafficScore != nil {
		features = append(features, analysis.Feature{Name: "traffic_overall_score", Value: trafficScore.OverallScore})
		features = append(features, analysis.Feature{Name: "traffic_change", Value: trafficScore.TrafficChange})
		features = append(features, analysis.Feature{Name: "search_change", Value: trafficScore.SearchChange})
		features = append(features, analysis.Feature{Name: "social_change", Value: trafficScore.SocialChange})
		features = append(features, analysis.Feature{Name: "traffic_recent_trend", Value: trafficScore.RecentTrend})
		features = append(features, analysis.Feature{Name: "traffic_confidence", Value: trafficScore.ConfidenceScore})
	}

	// 使用所有模型进行预测
	modelPredictions, err := p.modelManager.PredictWithAllModels(features, symbol, predictionTime, targetTime)
	if err != nil {
		return nil, fmt.Errorf("模型预测失败: %w", err)
	}

	// 合并预测结果
	combinedPrediction := p.modelManager.CombinePredictions(modelPredictions)
	if combinedPrediction == nil {
		return nil, fmt.Errorf("合并预测结果失败")
	}

	// 调整预测结果，考虑舆情和流量因素
	p.adjustPrediction(combinedPrediction, sentimentScore, trafficScore)

	// 转换为数据模型
	predictionData := ConvertPredictionToModel(combinedPrediction)

	return predictionData, nil
}

// adjustPrediction 根据舆情和流量数据调整预测结果
func (p *Predictor) adjustPrediction(prediction *PredictionResult, sentimentScore *analysis.SentimentScore, trafficScore *analysis.TrafficScore) {
	// 初始权重
	technicalWeight := 0.7
	sentimentWeight := 0.2
	trafficWeight := 0.1

	// 调整权重
	totalWeight := technicalWeight
	sentimentFactor := 0.0
	trafficFactor := 0.0

	// 考虑舆情因素
	if sentimentScore != nil {
		// 舆情得分范围从[-1,1]映射到[0,1]
		sentimentValue := (sentimentScore.OverallScore + 1) / 2
		
		// 舆情因子：正值表示看涨，负值表示看跌
		sentimentFactor = (sentimentValue - 0.5) * 2
		
		// 根据置信度调整权重
		sentimentWeight *= sentimentScore.ConfidenceScore
		totalWeight += sentimentWeight
	} else {
		sentimentWeight = 0
	}

	// 考虑流量因素
	if trafficScore != nil {
		// 流量得分范围从[0,100]映射到[0,1]
		trafficValue := trafficScore.OverallScore / 100
		
		// 流量因子：正值表示看涨，负值表示看跌
		trafficFactor = (trafficValue - 0.5) * 2
		
		// 根据置信度调整权重
		trafficWeight *= trafficScore.ConfidenceScore
		totalWeight += trafficWeight
	} else {
		trafficWeight = 0
	}

	// 技术分析因子
	technicalFactor := 0.0
	if prediction.Direction == Up {
		technicalFactor = prediction.Confidence
	} else if prediction.Direction == Down {
		technicalFactor = -prediction.Confidence
	} else {
		technicalFactor = 0
	}

	// 计算综合因子
	combinedFactor := (technicalFactor*technicalWeight + sentimentFactor*sentimentWeight + trafficFactor*trafficWeight) / totalWeight

	// 调整预测方向
	if combinedFactor > 0.3 {
		prediction.Direction = Up
		prediction.PredictedChange = math.Min(5.0, combinedFactor * 5) // 最大5%
	} else if combinedFactor < -0.3 {
		prediction.Direction = Down
		prediction.PredictedChange = math.Max(-5.0, combinedFactor * 5) // 最小-5%
	} else {
		prediction.Direction = Neutral
		prediction.PredictedChange = combinedFactor * 2 // 范围-0.6%至0.6%
	}

	// 调整置信度
	// 如果各因素方向一致，提高置信度；如果方向不一致，降低置信度
	directionConsistency := 1.0
	if (technicalFactor > 0 && sentimentFactor < 0) || (technicalFactor < 0 && sentimentFactor > 0) {
		directionConsistency *= 0.8
	}
	if (technicalFactor > 0 && trafficFactor < 0) || (technicalFactor < 0 && trafficFactor > 0) {
		directionConsistency *= 0.8
	}
	if (sentimentFactor > 0 && trafficFactor < 0) || (sentimentFactor < 0 && trafficFactor > 0) {
		directionConsistency *= 0.9
	}

	prediction.Confidence = math.Min(1.0, prediction.Confidence * directionConsistency)

	// 添加舆情和流量特征到预测结果
	if sentimentScore != nil {
		prediction.Features["sentiment_overall_score"] = sentimentScore.OverallScore
		prediction.Features["sentiment_recent_trend"] = sentimentScore.RecentTrend
		prediction.Features["sentiment_confidence"] = sentimentScore.ConfidenceScore
	}
	if trafficScore != nil {
		prediction.Features["traffic_overall_score"] = trafficScore.OverallScore
		prediction.Features["traffic_recent_trend"] = trafficScore.RecentTrend
		prediction.Features["traffic_confidence"] = trafficScore.ConfidenceScore
	}
}

// EvaluatePrediction 评估预测结果
func (p *Predictor) EvaluatePrediction(prediction *models.PredictionData, actualPrice float64) {
	// 计算实际变化
	prediction.ActualPrice = actualPrice
	if prediction.ActualPrice > 0 {
		// 获取预测时的价格
		var predictedPrice float64
		if priceFeature, ok := prediction.FeaturesMap["close"]; ok {
			predictedPrice = priceFeature
		}

		if predictedPrice > 0 {
			prediction.ActualChange = (actualPrice - predictedPrice) / predictedPrice * 100
		}
	}

	// 判断预测是否正确
	if prediction.Direction == "up" && prediction.ActualChange > 0 {
		prediction.IsCorrect = true
	} else if prediction.Direction == "down" && prediction.ActualChange < 0 {
		prediction.IsCorrect = true
	} else if prediction.Direction == "neutral" && math.Abs(prediction.ActualChange) < 0.5 {
		prediction.IsCorrect = true
	} else {
		prediction.IsCorrect = false
	}
}

