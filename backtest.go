package backtest

import (
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/analysis"
	"github.com/user/btc-predictor/internal/models"
	"github.com/user/btc-predictor/internal/prediction"
	"github.com/user/btc-predictor/internal/storage"
)

// BacktestConfig 表示回测配置
type BacktestConfig struct {
	Symbol           string        // 交易对符号
	Interval         string        // K线间隔
	StartTime        time.Time     // 回测开始时间
	EndTime          time.Time     // 回测结束时间
	PredictionWindow time.Duration // 预测窗口
	InitialCapital   float64       // 初始资金
	TradeFee         float64       // 交易手续费率
	LeverageRatio    float64       // 杠杆比例
	StopLossPercent  float64       // 止损百分比
	TakeProfitPercent float64      // 止盈百分比
	RiskPerTrade     float64       // 每笔交易风险比例
}

// BacktestResult 表示回测结果
type BacktestResult struct {
	TotalTrades      int       // 总交易次数
	WinningTrades    int       // 盈利交易次数
	LosingTrades     int       // 亏损交易次数
	WinRate          float64   // 胜率
	ProfitFactor     float64   // 盈亏比
	TotalReturn      float64   // 总收益
	AnnualizedReturn float64   // 年化收益率
	MaxDrawdown      float64   // 最大回撤
	SharpeRatio      float64   // 夏普比率
	StartTime        time.Time // 回测开始时间
	EndTime          time.Time // 回测结束时间
	Duration         string    // 回测持续时间
	FinalCapital     float64   // 最终资金
	Trades           []Trade   // 交易记录
}

// Trade 表示交易记录
type Trade struct {
	EntryTime      time.Time // 入场时间
	ExitTime       time.Time // 出场时间
	Direction      string    // 交易方向
	EntryPrice     float64   // 入场价格
	ExitPrice      float64   // 出场价格
	Quantity       float64   // 交易数量
	ProfitLoss     float64   // 盈亏金额
	ProfitLossPerc float64   // 盈亏百分比
	Fee            float64   // 手续费
	NetProfitLoss  float64   // 净盈亏
}

// Position 表示持仓
type Position struct {
	Symbol        string    // 交易对符号
	Direction     string    // 持仓方向
	EntryTime     time.Time // 入场时间
	EntryPrice    float64   // 入场价格
	Quantity      float64   // 持仓数量
	StopLoss      float64   // 止损价格
	TakeProfit    float64   // 止盈价格
	CurrentPrice  float64   // 当前价格
	UnrealizedPnL float64   // 未实现盈亏
}

// Backtester 表示回测器
type Backtester struct {
	log                *logrus.Logger
	store              storage.Storage
	predictor          *prediction.Predictor
	indicatorCalculator *analysis.IndicatorCalculator
	featureExtractor   *analysis.FeatureExtractor
	config             BacktestConfig
	capital            float64
	position           *Position
	trades             []Trade
	equity             []float64
	timestamps         []time.Time
}

// NewBacktester 创建一个新的回测器
func NewBacktester(
	log *logrus.Logger,
	store storage.Storage,
	predictor *prediction.Predictor,
	indicatorCalculator *analysis.IndicatorCalculator,
	featureExtractor *analysis.FeatureExtractor,
	config BacktestConfig,
) *Backtester {
	return &Backtester{
		log:                log,
		store:              store,
		predictor:          predictor,
		indicatorCalculator: indicatorCalculator,
		featureExtractor:   featureExtractor,
		config:             config,
		capital:            config.InitialCapital,
		trades:             make([]Trade, 0),
		equity:             make([]float64, 0),
		timestamps:         make([]time.Time, 0),
	}
}

// Run 运行回测
func (b *Backtester) Run() (*BacktestResult, error) {
	b.log.WithFields(logrus.Fields{
		"symbol":     b.config.Symbol,
		"start_time": b.config.StartTime,
		"end_time":   b.config.EndTime,
	}).Info("开始回测")

	// 获取回测时间范围内的K线数据
	klines, err := b.store.GetKLinesByTimeRange(
		b.config.Symbol,
		b.config.Interval,
		b.config.StartTime,
		b.config.EndTime,
	)
	if err != nil {
		return nil, fmt.Errorf("获取K线数据失败: %w", err)
	}

	if len(klines) == 0 {
		return nil, fmt.Errorf("回测时间范围内没有K线数据")
	}

	b.log.WithField("klines_count", len(klines)).Info("获取到K线数据")

	// 初始化回测状态
	b.capital = b.config.InitialCapital
	b.position = nil
	b.trades = make([]Trade, 0)
	b.equity = make([]float64, 0)
	b.timestamps = make([]time.Time, 0)

	// 记录初始资金
	b.equity = append(b.equity, b.capital)
	b.timestamps = append(b.timestamps, klines[0].OpenTime)

	// 遍历K线数据
	for i := 0; i < len(klines)-1; i++ {
		// 获取当前K线和下一个K线
		currentKline := klines[i]
		nextKline := klines[i+1]

		// 更新当前时间和价格
		currentTime := currentKline.OpenTime
		currentPrice := currentKline.Close

		// 如果有持仓，检查是否触发止损或止盈
		if b.position != nil {
			// 检查是否触发止损
			if (b.position.Direction == "long" && currentPrice <= b.position.StopLoss) ||
				(b.position.Direction == "short" && currentPrice >= b.position.StopLoss) {
				b.closePosition(currentTime, b.position.StopLoss)
				b.log.WithFields(logrus.Fields{
					"time":  currentTime,
					"price": b.position.StopLoss,
				}).Info("触发止损")
			}

			// 检查是否触发止盈
			if (b.position.Direction == "long" && currentPrice >= b.position.TakeProfit) ||
				(b.position.Direction == "short" && currentPrice <= b.position.TakeProfit) {
				b.closePosition(currentTime, b.position.TakeProfit)
				b.log.WithFields(logrus.Fields{
					"time":  currentTime,
					"price": b.position.TakeProfit,
				}).Info("触发止盈")
			}
		}

		// 每隔一定时间进行一次预测
		if i%10 == 0 { // 假设每10个K线进行一次预测
			// 获取历史数据用于预测
			historyKlines := klines[max(0, i-500):i+1]

			// 计算技术指标
			indicators, err := b.indicatorCalculator.CalculateIndicators(historyKlines)
			if err != nil {
				b.log.WithError(err).Warn("计算技术指标失败")
				continue
			}

			// 计算自定义指标
			customIndicators, err := b.indicatorCalculator.CalculateCustomIndicators(historyKlines)
			if err != nil {
				b.log.WithError(err).Warn("计算自定义指标失败")
				continue
			}

			// 合并所有指标
			for name, values := range customIndicators {
				indicators[name] = values
			}

			// 提取特征
			features, err := b.featureExtractor.ExtractFeatures(historyKlines, indicators, len(historyKlines)-1)
			if err != nil {
				b.log.WithError(err).Warn("提取特征失败")
				continue
			}

			// 使用预测模型进行预测
			predictionResult := b.simulatePrediction(features)

			// 根据预测结果进行交易
			b.trade(currentTime, currentPrice, predictionResult)
		}

		// 更新持仓的未实现盈亏
		if b.position != nil {
			b.position.CurrentPrice = currentPrice
			if b.position.Direction == "long" {
				b.position.UnrealizedPnL = (currentPrice - b.position.EntryPrice) * b.position.Quantity
			} else {
				b.position.UnrealizedPnL = (b.position.EntryPrice - currentPrice) * b.position.Quantity
			}
		}

		// 记录每个时间点的资金
		equity := b.capital
		if b.position != nil {
			equity += b.position.UnrealizedPnL
		}
		b.equity = append(b.equity, equity)
		b.timestamps = append(b.timestamps, currentTime)
	}

	// 平掉最后的持仓
	if b.position != nil {
		lastKline := klines[len(klines)-1]
		b.closePosition(lastKline.OpenTime, lastKline.Close)
	}

	// 计算回测结果
	result := b.calculateResults()

	b.log.WithFields(logrus.Fields{
		"total_trades":   result.TotalTrades,
		"win_rate":       result.WinRate,
		"total_return":   result.TotalReturn,
		"max_drawdown":   result.MaxDrawdown,
		"final_capital":  result.FinalCapital,
	}).Info("回测完成")

	return result, nil
}

// simulatePrediction 模拟预测
func (b *Backtester) simulatePrediction(features []analysis.Feature) *prediction.PredictionResult {
	// 将特征转换为映射
	featureMap := analysis.FeaturesToMap(features)

	// 创建一个简单的预测结果
	var direction prediction.Direction
	var predictedChange float64
	var confidence float64

	// 根据RSI指标进行简单预测
	if rsi, ok := featureMap["rsi14"]; ok {
		if rsi > 70 {
			direction = prediction.Down
			predictedChange = -1.0
			confidence = 0.7
		} else if rsi < 30 {
			direction = prediction.Up
			predictedChange = 1.0
			confidence = 0.7
		} else {
			direction = prediction.Neutral
			predictedChange = 0.0
			confidence = 0.5
		}
	} else {
		// 如果没有RSI指标，使用MACD
		if macdHist, ok := featureMap["macd_histogram"]; ok {
			if macdHist > 0 {
				direction = prediction.Up
				predictedChange = 0.5
				confidence = 0.6
			} else if macdHist < 0 {
				direction = prediction.Down
				predictedChange = -0.5
				confidence = 0.6
			} else {
				direction = prediction.Neutral
				predictedChange = 0.0
				confidence = 0.5
			}
		} else {
			// 默认中性
			direction = prediction.Neutral
			predictedChange = 0.0
			confidence = 0.5
		}
	}

	// 创建预测结果
	result := &prediction.PredictionResult{
		Direction:       direction,
		PredictedChange: predictedChange,
		Confidence:      confidence,
		ModelName:       "backtest_model",
		Features:        featureMap,
	}

	return result
}

// trade 根据预测结果进行交易
func (b *Backtester) trade(time time.Time, price float64, prediction *prediction.PredictionResult) {
	// 如果预测置信度不够，不进行交易
	if prediction.Confidence < 0.6 {
		return
	}

	// 如果已有持仓
	if b.position != nil {
		// 如果预测方向与持仓方向相反，平仓
		if (b.position.Direction == "long" && prediction.Direction == prediction.Down) ||
			(b.position.Direction == "short" && prediction.Direction == prediction.Up) {
			b.closePosition(time, price)
		}
		// 如果预测方向与持仓方向相同，保持持仓
		return
	}

	// 如果没有持仓，根据预测方向开仓
	if prediction.Direction == prediction.Up {
		b.openPosition("long", time, price)
	} else if prediction.Direction == prediction.Down {
		b.openPosition("short", time, price)
	}
}

// openPosition 开仓
func (b *Backtester) openPosition(direction string, time time.Time, price float64) {
	// 计算交易数量
	riskAmount := b.capital * b.config.RiskPerTrade
	stopLossPrice := 0.0
	if direction == "long" {
		stopLossPrice = price * (1 - b.config.StopLossPercent/100)
	} else {
		stopLossPrice = price * (1 + b.config.StopLossPercent/100)
	}
	riskPerUnit := math.Abs(price - stopLossPrice)
	quantity := riskAmount / riskPerUnit * b.config.LeverageRatio

	// 计算止盈价格
	takeProfitPrice := 0.0
	if direction == "long" {
		takeProfitPrice = price * (1 + b.config.TakeProfitPercent/100)
	} else {
		takeProfitPrice = price * (1 - b.config.TakeProfitPercent/100)
	}

	// 创建持仓
	b.position = &Position{
		Symbol:     b.config.Symbol,
		Direction:  direction,
		EntryTime:  time,
		EntryPrice: price,
		Quantity:   quantity,
		StopLoss:   stopLossPrice,
		TakeProfit: takeProfitPrice,
	}

	// 计算并扣除手续费
	fee := price * quantity * b.config.TradeFee / 100
	b.capital -= fee

	b.log.WithFields(logrus.Fields{
		"time":      time,
		"direction": direction,
		"price":     price,
		"quantity":  quantity,
		"stop_loss": stopLossPrice,
		"fee":       fee,
	}).Info("开仓")
}

// closePosition 平仓
func (b *Backtester) closePosition(time time.Time, price float64) {
	if b.position == nil {
		return
	}

	// 计算盈亏
	profitLoss := 0.0
	if b.position.Direction == "long" {
		profitLoss = (price - b.position.EntryPrice) * b.position.Quantity
	} else {
		profitLoss = (b.position.EntryPrice - price) * b.position.Quantity
	}

	// 计算手续费
	fee := price * b.position.Quantity * b.config.TradeFee / 100

	// 计算净盈亏
	netProfitLoss := profitLoss - fee

	// 更新资金
	b.capital += profitLoss - fee

	// 记录交易
	trade := Trade{
		EntryTime:      b.position.EntryTime,
		ExitTime:       time,
		Direction:      b.position.Direction,
		EntryPrice:     b.position.EntryPrice,
		ExitPrice:      price,
		Quantity:       b.position.Quantity,
		ProfitLoss:     profitLoss,
		ProfitLossPerc: profitLoss / (b.position.EntryPrice * b.position.Quantity) * 100,
		Fee:            fee,
		NetProfitLoss:  netProfitLoss,
	}
	b.trades = append(b.trades, trade)

	b.log.WithFields(logrus.Fields{
		"time":           time,
		"direction":      b.position.Direction,
		"entry_price":    b.position.EntryPrice,
		"exit_price":     price,
		"profit_loss":    profitLoss,
		"profit_loss_%": trade.ProfitLossPerc,
		"fee":            fee,
		"net_profit":     netProfitLoss,
	}).Info("平仓")

	// 清空持仓
	b.position = nil
}

// calculateResults 计算回测结果
func (b *Backtester) calculateResults() *BacktestResult {
	result := &BacktestResult{
		TotalTrades:  len(b.trades),
		WinningTrades: 0,
		LosingTrades: 0,
		StartTime:    b.config.StartTime,
		EndTime:      b.config.EndTime,
		Duration:     b.config.EndTime.Sub(b.config.StartTime).String(),
		FinalCapital: b.capital,
		Trades:       b.trades,
	}

	// 计算胜率和盈亏比
	totalProfit := 0.0
	totalLoss := 0.0
	for _, trade := range b.trades {
		if trade.NetProfitLoss > 0 {
			result.WinningTrades++
			totalProfit += trade.NetProfitLoss
		} else {
			result.LosingTrades++
			totalLoss += math.Abs(trade.NetProfitLoss)
		}
	}

	if result.TotalTrades > 0 {
		result.WinRate = float64(result.WinningTrades) / float64(result.TotalTrades)
	}

	if totalLoss > 0 {
		result.ProfitFactor = totalProfit / totalLoss
	} else {
		result.ProfitFactor = totalProfit
	}

	// 计算总收益和年化收益率
	result.TotalReturn = (b.capital - b.config.InitialCapital) / b.config.InitialCapital * 100
	durationYears := b.config.EndTime.Sub(b.config.StartTime).Hours() / 24 / 365
	if durationYears > 0 {
		result.AnnualizedReturn = math.Pow(1+result.TotalReturn/100, 1/durationYears) - 1
		result.AnnualizedReturn *= 100
	}

	// 计算最大回撤
	maxEquity := b.equity[0]
	maxDrawdown := 0.0
	for _, equity := range b.equity {
		if equity > maxEquity {
			maxEquity = equity
		}
		drawdown := (maxEquity - equity) / maxEquity * 100
		if drawdown > maxDrawdown {
			maxDrawdown = drawdown
		}
	}
	result.MaxDrawdown = maxDrawdown

	// 计算夏普比率
	if len(b.equity) > 1 {
		returns := make([]float64, len(b.equity)-1)
		for i := 1; i < len(b.equity); i++ {
			returns[i-1] = (b.equity[i] - b.equity[i-1]) / b.equity[i-1]
		}
		avgReturn := mean(returns)
		stdReturn := stdDev(returns, avgReturn)
		if stdReturn > 0 {
			result.SharpeRatio = avgReturn / stdReturn * math.Sqrt(252) // 假设一年有252个交易日
		}
	}

	return result
}

// mean 计算平均值
func mean(values []float64) float64 {
	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

// stdDev 计算标准差
func stdDev(values []float64, mean float64) float64 {
	sum := 0.0
	for _, v := range values {
		sum += (v - mean) * (v - mean)
	}
	return math.Sqrt(sum / float64(len(values)))
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

