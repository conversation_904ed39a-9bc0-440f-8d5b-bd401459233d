import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || '';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 价格数据API
export const getPriceChartData = async (symbol, interval, limit) => {
  try {
    const response = await api.get(`/api/v1/visualization/price-chart/${symbol}`, {
      params: { interval, limit },
    });
    return response.data;
  } catch (error) {
    console.error('获取价格图表数据失败:', error);
    throw error;
  }
};

// 预测数据API
export const getPredictionChartData = async (symbol, limit) => {
  try {
    const response = await api.get(`/api/v1/visualization/prediction-chart/${symbol}`, {
      params: { limit },
    });
    return response.data;
  } catch (error) {
    console.error('获取预测图表数据失败:', error);
    throw error;
  }
};

// 准确率数据API
export const getAccuracyChartData = async (symbol, days) => {
  try {
    const response = await api.get(`/api/v1/visualization/accuracy-chart/${symbol}`, {
      params: { days },
    });
    return response.data;
  } catch (error) {
    console.error('获取准确率图表数据失败:', error);
    throw error;
  }
};

// 性能数据API
export const getPerformanceChartData = async (symbol, days) => {
  try {
    const response = await api.get(`/api/v1/visualization/performance-chart/${symbol}`, {
      params: { days },
    });
    return response.data;
  } catch (error) {
    console.error('获取性能图表数据失败:', error);
    throw error;
  }
};

// 舆情数据API
export const getSentimentChartData = async (days) => {
  try {
    const response = await api.get('/api/v1/visualization/sentiment-chart', {
      params: { days },
    });
    return response.data;
  } catch (error) {
    console.error('获取舆情图表数据失败:', error);
    throw error;
  }
};

// 流量数据API
export const getTrafficChartData = async (days) => {
  try {
    const response = await api.get('/api/v1/visualization/traffic-chart', {
      params: { days },
    });
    return response.data;
  } catch (error) {
    console.error('获取流量图表数据失败:', error);
    throw error;
  }
};

// 仪表板数据API
export const getDashboardData = async (symbol) => {
  try {
    const response = await api.get('/api/v1/visualization/dashboard', {
      params: { symbol },
    });
    return response.data;
  } catch (error) {
    console.error('获取仪表板数据失败:', error);
    throw error;
  }
};

// 获取最新预测API
export const getLatestPrediction = async (symbol) => {
  try {
    const response = await api.get(`/api/v1/predictions/latest/${symbol}`);
    return response.data;
  } catch (error) {
    console.error('获取最新预测失败:', error);
    throw error;
  }
};

// 获取预测列表API
export const getPredictions = async (symbol, limit, offset) => {
  try {
    const response = await api.get(`/api/v1/predictions/${symbol}`, {
      params: { limit, offset },
    });
    return response.data;
  } catch (error) {
    console.error('获取预测列表失败:', error);
    throw error;
  }
};

export default api;

