package prediction

import (
	"encoding/json"
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/analysis"
	"github.com/user/btc-predictor/internal/models"
)

// Direction 表示价格方向
type Direction string

const (
	Up      Direction = "up"      // 上涨
	Down    Direction = "down"    // 下跌
	Neutral Direction = "neutral" // 中性
)

// PredictionResult 表示预测结果
type PredictionResult struct {
	Symbol          string    // 交易对符号
	PredictionTime  time.Time // 预测时间
	TargetTime      time.Time // 目标时间
	Direction       Direction // 预测方向
	PredictedChange float64   // 预测变化百分比
	Confidence      float64   // 置信度
	ModelName       string    // 模型名称
	Features        map[string]float64 // 使用的特征
}

// PredictionModel 表示预测模型接口
type PredictionModel interface {
	Name() string
	Predict(features []analysis.Feature) (*PredictionResult, error)
	Train(features [][]analysis.Feature, labels []float64) error
	Save(path string) error
	Load(path string) error
}

// ModelManager 表示模型管理器
type ModelManager struct {
	log    *logrus.Logger
	models []PredictionModel
}

// NewModelManager 创建一个新的模型管理器
func NewModelManager(log *logrus.Logger) *ModelManager {
	return &ModelManager{
		log:    log,
		models: make([]PredictionModel, 0),
	}
}

// RegisterModel 注册一个预测模型
func (m *ModelManager) RegisterModel(model PredictionModel) {
	m.models = append(m.models, model)
	m.log.WithField("model", model.Name()).Info("注册预测模型")
}

// GetModel 获取指定名称的预测模型
func (m *ModelManager) GetModel(name string) (PredictionModel, error) {
	for _, model := range m.models {
		if model.Name() == name {
			return model, nil
		}
	}
	return nil, fmt.Errorf("未找到名为 %s 的预测模型", name)
}

// GetAllModels 获取所有预测模型
func (m *ModelManager) GetAllModels() []PredictionModel {
	return m.models
}

// PredictWithAllModels 使用所有模型进行预测
func (m *ModelManager) PredictWithAllModels(features []analysis.Feature, symbol string, predictionTime, targetTime time.Time) ([]*PredictionResult, error) {
	results := make([]*PredictionResult, 0, len(m.models))

	for _, model := range m.models {
		result, err := model.Predict(features)
		if err != nil {
			m.log.WithError(err).WithField("model", model.Name()).Error("模型预测失败")
			continue
		}

		// 设置预测结果的基本信息
		result.Symbol = symbol
		result.PredictionTime = predictionTime
		result.TargetTime = targetTime

		results = append(results, result)
	}

	return results, nil
}

// CombinePredictions 合并多个模型的预测结果
func (m *ModelManager) CombinePredictions(predictions []*PredictionResult) *PredictionResult {
	if len(predictions) == 0 {
		return nil
	}

	// 使用加权平均方法合并预测结果
	var weightedChangeSum, weightSum float64
	directionVotes := make(map[Direction]float64)

	for _, pred := range predictions {
		// 使用置信度作为权重
		weight := pred.Confidence

		// 累加加权变化
		weightedChangeSum += pred.PredictedChange * weight
		weightSum += weight

		// 累加方向投票
		directionVotes[pred.Direction] += weight
	}

	// 计算加权平均变化
	var avgChange float64
	if weightSum > 0 {
		avgChange = weightedChangeSum / weightSum
	}

	// 确定最终方向
	var finalDirection Direction
	var maxVote float64
	for direction, vote := range directionVotes {
		if vote > maxVote {
			maxVote = vote
			finalDirection = direction
		}
	}

	// 如果方向投票不明确，根据平均变化确定方向
	if maxVote < weightSum*0.6 {
		if avgChange > 0.5 {
			finalDirection = Up
		} else if avgChange < -0.5 {
			finalDirection = Down
		} else {
			finalDirection = Neutral
		}
	}

	// 计算综合置信度
	// 如果模型预测结果一致，置信度更高
	var combinedConfidence float64
	if len(directionVotes) == 1 {
		// 所有模型预测方向一致
		combinedConfidence = math.Min(1.0, weightSum/float64(len(predictions))*1.2)
	} else if len(directionVotes) == 2 && directionVotes[Neutral] > 0 {
		// 部分模型预测中性，部分模型预测同一方向
		if directionVotes[Up] > 0 && directionVotes[Down] == 0 {
			combinedConfidence = math.Min(1.0, directionVotes[Up]/weightSum)
		} else if directionVotes[Down] > 0 && directionVotes[Up] == 0 {
			combinedConfidence = math.Min(1.0, directionVotes[Down]/weightSum)
		} else {
			combinedConfidence = math.Min(1.0, maxVote/weightSum)
		}
	} else {
		// 模型预测方向不一致
		combinedConfidence = math.Min(1.0, maxVote/weightSum*0.8)
	}

	// 创建综合预测结果
	result := &PredictionResult{
		Symbol:          predictions[0].Symbol,
		PredictionTime:  predictions[0].PredictionTime,
		TargetTime:      predictions[0].TargetTime,
		Direction:       finalDirection,
		PredictedChange: avgChange,
		Confidence:      combinedConfidence,
		ModelName:       "ensemble",
		Features:        make(map[string]float64),
	}

	// 合并特征
	for _, pred := range predictions {
		for name, value := range pred.Features {
			result.Features[name] = value
		}
	}

	return result
}

// SaveModels 保存所有模型
func (m *ModelManager) SaveModels(dirPath string) error {
	for _, model := range m.models {
		path := fmt.Sprintf("%s/%s.model", dirPath, model.Name())
		if err := model.Save(path); err != nil {
			return fmt.Errorf("保存模型 %s 失败: %w", model.Name(), err)
		}
	}
	return nil
}

// LoadModels 加载所有模型
func (m *ModelManager) LoadModels(dirPath string) error {
	for _, model := range m.models {
		path := fmt.Sprintf("%s/%s.model", dirPath, model.Name())
		if err := model.Load(path); err != nil {
			return fmt.Errorf("加载模型 %s 失败: %w", model.Name(), err)
		}
	}
	return nil
}

// ConvertPredictionToModel 将预测结果转换为数据模型
func ConvertPredictionToModel(pred *PredictionResult) *models.PredictionData {
	featuresJSON, _ := json.Marshal(pred.Features)
	
	return &models.PredictionData{
		Symbol:          pred.Symbol,
		PredictionTime:  pred.PredictionTime,
		TargetTime:      pred.TargetTime,
		Direction:       string(pred.Direction),
		PredictedChange: pred.PredictedChange,
		Confidence:      pred.Confidence,
		ModelName:       pred.ModelName,
		Features:        string(featuresJSON),
		FeaturesMap:     pred.Features,
	}
}

