package binance

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/adshao/go-binance/v2"
	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/pkg/config"
)

// Client 表示币安API客户端
type Client struct {
	client    *binance.Client
	wsClient  *binance.WebsocketClient
	log       *logrus.Logger
	config    *config.Config
	useTestnet bool
}

// ClientConfig 表示币安客户端配置
type ClientConfig struct {
	APIKey     string
	APISecret  string
	UseTestnet bool
	Log        *logrus.Logger
	Config     *config.Config
}

// New 创建一个新的币安API客户端
func New(cfg ClientConfig) *Client {
	// 设置API密钥
	apiKey := cfg.APIKey
	if apiKey == "" {
		apiKey = os.Getenv("BINANCE_API_KEY")
	}

	apiSecret := cfg.APISecret
	if apiSecret == "" {
		apiSecret = os.Getenv("BINANCE_API_SECRET")
	}

	// 创建客户端
	client := binance.NewClient(apiKey, apiSecret)
	wsClient := binance.NewWebsocketClient(false)

	// 如果使用测试网络
	if cfg.UseTestnet {
		client.UseTestnet = true
		wsClient.UseTestnet = true
	}

	return &Client{
		client:    client,
		wsClient:  wsClient,
		log:       cfg.Log,
		config:    cfg.Config,
		useTestnet: cfg.UseTestnet,
	}
}

// GetKlines 获取K线数据
func (c *Client) GetKlines(symbol string, interval string, startTime, endTime time.Time, limit int) ([]*binance.Kline, error) {
	if limit <= 0 || limit > 1000 {
		limit = 500 // 默认值
	}

	service := c.client.NewKlinesService().
		Symbol(symbol).
		Interval(interval).
		Limit(limit)

	if !startTime.IsZero() {
		service = service.StartTime(startTime.UnixNano() / int64(time.Millisecond))
	}

	if !endTime.IsZero() {
		service = service.EndTime(endTime.UnixNano() / int64(time.Millisecond))
	}

	klines, err := service.Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取K线数据失败: %w", err)
	}

	return klines, nil
}

// GetOrderBook 获取订单簿数据
func (c *Client) GetOrderBook(symbol string, limit int) (*binance.DepthResponse, error) {
	if limit <= 0 || limit > 5000 {
		limit = 100 // 默认值
	}

	depth, err := c.client.NewDepthService().
		Symbol(symbol).
		Limit(limit).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取订单簿数据失败: %w", err)
	}

	return depth, nil
}

// GetTicker24h 获取24小时价格变动统计数据
func (c *Client) GetTicker24h(symbol string) (*binance.TickerStatistics, error) {
	ticker, err := c.client.NewTickerService().
		Symbol(symbol).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取24小时价格变动统计数据失败: %w", err)
	}

	return ticker, nil
}

// GetTrades 获取最近的交易
func (c *Client) GetTrades(symbol string, limit int) ([]*binance.TradeV3, error) {
	if limit <= 0 || limit > 1000 {
		limit = 500 // 默认值
	}

	trades, err := c.client.NewHistoricalTradesService().
		Symbol(symbol).
		Limit(limit).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取最近交易失败: %w", err)
	}

	return trades, nil
}

// SubscribeKlines 订阅K线数据
func (c *Client) SubscribeKlines(symbol string, interval string) (chan *binance.WsKlineEvent, chan struct{}, error) {
	wsKlineHandler := func(event *binance.WsKlineEvent) {
		c.log.WithFields(logrus.Fields{
			"symbol":   event.Symbol,
			"interval": event.Kline.Interval,
			"time":     event.Time,
		}).Debug("收到K线数据")
	}

	errHandler := func(err error) {
		c.log.WithError(err).Error("K线WebSocket错误")
	}

	doneC, stopC, err := binance.WsKlineServe(symbol, interval, wsKlineHandler, errHandler)
	if err != nil {
		return nil, nil, fmt.Errorf("订阅K线数据失败: %w", err)
	}

	// 创建一个通道来传递K线事件
	klineC := make(chan *binance.WsKlineEvent, 100)

	// 启动一个goroutine来处理WebSocket消息
	go func() {
		defer close(klineC)
		<-doneC
	}()

	return klineC, stopC, nil
}

// SubscribeDepth 订阅订单簿数据
func (c *Client) SubscribeDepth(symbol string) (chan *binance.WsDepthEvent, chan struct{}, error) {
	wsDepthHandler := func(event *binance.WsDepthEvent) {
		c.log.WithFields(logrus.Fields{
			"symbol":        event.Symbol,
			"firstUpdateID": event.FirstUpdateID,
			"finalUpdateID": event.FinalUpdateID,
			"time":          event.Time,
		}).Debug("收到订单簿数据")
	}

	errHandler := func(err error) {
		c.log.WithError(err).Error("订单簿WebSocket错误")
	}

	doneC, stopC, err := binance.WsDepthServe(symbol, wsDepthHandler, errHandler)
	if err != nil {
		return nil, nil, fmt.Errorf("订阅订单簿数据失败: %w", err)
	}

	// 创建一个通道来传递订单簿事件
	depthC := make(chan *binance.WsDepthEvent, 100)

	// 启动一个goroutine来处理WebSocket消息
	go func() {
		defer close(depthC)
		<-doneC
	}()

	return depthC, stopC, nil
}

// SubscribeTrades 订阅交易数据
func (c *Client) SubscribeTrades(symbol string) (chan *binance.WsTradeEvent, chan struct{}, error) {
	wsTradeHandler := func(event *binance.WsTradeEvent) {
		c.log.WithFields(logrus.Fields{
			"symbol":    event.Symbol,
			"tradeID":   event.TradeID,
			"price":     event.Price,
			"quantity":  event.Quantity,
			"time":      event.Time,
		}).Debug("收到交易数据")
	}

	errHandler := func(err error) {
		c.log.WithError(err).Error("交易WebSocket错误")
	}

	doneC, stopC, err := binance.WsTradeServe(symbol, wsTradeHandler, errHandler)
	if err != nil {
		return nil, nil, fmt.Errorf("订阅交易数据失败: %w", err)
	}

	// 创建一个通道来传递交易事件
	tradeC := make(chan *binance.WsTradeEvent, 100)

	// 启动一个goroutine来处理WebSocket消息
	go func() {
		defer close(tradeC)
		<-doneC
	}()

	return tradeC, stopC, nil
}

// CreateOrder 创建订单
func (c *Client) CreateOrder(symbol string, side binance.SideType, orderType binance.OrderType, quantity float64, price float64) (*binance.CreateOrderResponse, error) {
	// 将数量和价格转换为字符串
	quantityStr := strconv.FormatFloat(quantity, 'f', -1, 64)
	priceStr := strconv.FormatFloat(price, 'f', -1, 64)

	// 创建订单服务
	orderService := c.client.NewCreateOrderService().
		Symbol(symbol).
		Side(side).
		Type(orderType).
		Quantity(quantityStr)

	// 根据订单类型设置价格
	if orderType == binance.OrderTypeLimit {
		orderService = orderService.TimeInForce(binance.TimeInForceTypeGTC).Price(priceStr)
	}

	// 执行订单
	order, err := orderService.Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("创建订单失败: %w", err)
	}

	return order, nil
}

// CancelOrder 取消订单
func (c *Client) CancelOrder(symbol string, orderID int64) (*binance.CancelOrderResponse, error) {
	// 取消订单
	cancelResponse, err := c.client.NewCancelOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("取消订单失败: %w", err)
	}

	return cancelResponse, nil
}

// GetOrder 获取订单信息
func (c *Client) GetOrder(symbol string, orderID int64) (*binance.Order, error) {
	// 获取订单信息
	order, err := c.client.NewGetOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取订单信息失败: %w", err)
	}

	return order, nil
}

// GetAccountInfo 获取账户信息
func (c *Client) GetAccountInfo() (*binance.Account, error) {
	// 获取账户信息
	account, err := c.client.NewGetAccountService().Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取账户信息失败: %w", err)
	}

	return account, nil
}

// GetExchangeInfo 获取交易所信息
func (c *Client) GetExchangeInfo() (*binance.ExchangeInfo, error) {
	// 获取交易所信息
	exchangeInfo, err := c.client.NewExchangeInfoService().Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取交易所信息失败: %w", err)
	}

	return exchangeInfo, nil
}

