# 币安API配置

# API访问配置
api:
  base_url: "https://api.binance.com"
  testnet_url: "https://testnet.binance.vision"
  use_testnet: false # 是否使用测试网络
  api_key: "" # 在.env文件中设置
  api_secret: "" # 在.env文件中设置
  
  # API请求配置
  request:
    timeout: 10s
    retry_count: 3
    retry_delay: 1s
    rate_limit: 20 # 每秒最大请求数

# WebSocket配置
websocket:
  base_url: "wss://stream.binance.com:9443/ws"
  testnet_url: "wss://testnet.binance.vision/ws"
  
  # 连接配置
  connection:
    ping_interval: 30s
    pong_wait: 10s
    reconnect_delay: 5s
    max_reconnect_attempts: 10
    
  # 订阅配置
  subscriptions:
    kline:
      enabled: true
      symbols: ["BTCUSDT"]
      intervals: ["1m"]
      buffer_size: 1000
      
    trade:
      enabled: true
      symbols: ["BTCUSDT"]
      buffer_size: 5000
      
    depth:
      enabled: true
      symbols: ["BTCUSDT"]
      levels: 20
      update_speed: 100ms
      buffer_size: 500
      
    ticker:
      enabled: true
      symbols: ["BTCUSDT"]
      buffer_size: 100

# 事件合约配置
event_contracts:
  enabled: true
  symbols: ["BTCUSDT"]
  timeframes: ["10m", "30m", "1h", "1d"]
  
  # 预测配置
  prediction:
    min_confidence: 0.7
    max_position_size: 0.1 # 账户余额的比例
    
  # 风险管理
  risk:
    max_daily_loss: 500 # USDT
    max_position_count: 5

