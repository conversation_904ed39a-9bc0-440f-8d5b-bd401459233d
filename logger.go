package logger

import (
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/sirupsen/logrus"
)

// Logger 表示日志记录器
type Logger struct {
	*logrus.Logger
}

// Config 表示日志配置
type Config struct {
	Level  string
	Format string
	Output string
}

// New 创建一个新的日志记录器
func New(config Config) *Logger {
	log := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	log.SetLevel(level)

	// 设置日志格式
	if strings.ToLower(config.Format) == "json" {
		log.SetFormatter(&logrus.JSONFormatter{
			CallerPrettyfier: callerPrettyfier,
		})
	} else {
		log.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:    true,
			CallerPrettyfier: callerPrettyfier,
		})
	}

	// 设置日志输出
	if config.Output != "" {
		// 确保日志目录存在
		logDir := filepath.Dir(config.Output)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			log.Warnf("无法创建日志目录: %v", err)
		}

		// 打开日志文件
		file, err := os.OpenFile(config.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			log.Warnf("无法打开日志文件: %v", err)
		} else {
			// 同时输出到文件和标准输出
			mw := io.MultiWriter(os.Stdout, file)
			log.SetOutput(mw)
		}
	}

	// 启用调用者信息
	log.SetReportCaller(true)

	return &Logger{log}
}

// callerPrettyfier 格式化调用者信息
func callerPrettyfier(f *runtime.Frame) (string, string) {
	filename := filepath.Base(f.File)
	return "", filename + ":" + strings.Split(f.Function, ".")[len(strings.Split(f.Function, "."))-1] + ":" + string(rune(f.Line))
}

// WithField 添加一个字段到日志条目
func (l *Logger) WithField(key string, value interface{}) *logrus.Entry {
	return l.Logger.WithField(key, value)
}

// WithFields 添加多个字段到日志条目
func (l *Logger) WithFields(fields logrus.Fields) *logrus.Entry {
	return l.Logger.WithFields(fields)
}

// WithError 添加错误到日志条目
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

