import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { Card, Select, Spin } from 'antd';
import { getPerformanceChartData } from '../services/api';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const { Option } = Select;

const PerformanceChart = ({ symbol = 'BTCUSDT' }) => {
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState(null);
  const [days, setDays] = useState(30);
  const [chartType, setChartType] = useState('returns');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await getPerformanceChartData(symbol, days);
        
        // 准备图表数据
        let datasets = [];
        
        if (chartType === 'returns') {
          datasets = [
            {
              label: '日收益率',
              data: data.returns,
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.5)',
              tension: 0.1,
            },
            {
              label: '累积收益率',
              data: data.cumulative_returns,
              borderColor: 'rgb(54, 162, 235)',
              backgroundColor: 'rgba(54, 162, 235, 0.5)',
              tension: 0.1,
              yAxisID: 'y1',
            },
          ];
        } else if (chartType === 'drawdowns') {
          datasets = [
            {
              label: '回撤率',
              data: data.drawdowns,
              borderColor: 'rgb(255, 99, 132)',
              backgroundColor: 'rgba(255, 99, 132, 0.5)',
              tension: 0.1,
            },
          ];
        } else if (chartType === 'sharpe') {
          datasets = [
            {
              label: '夏普比率',
              data: data.sharpe_ratios,
              borderColor: 'rgb(153, 102, 255)',
              backgroundColor: 'rgba(153, 102, 255, 0.5)',
              tension: 0.1,
            },
          ];
        }
        
        const chartData = {
          labels: data.labels,
          datasets: datasets,
        };
        
        setChartData(chartData);
      } catch (error) {
        console.error('加载性能图表数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [symbol, days, chartType]);

  const getOptions = () => {
    const baseOptions = {
      responsive: true,
      interaction: {
        mode: 'index',
        intersect: false,
      },
      stacked: false,
      plugins: {
        title: {
          display: true,
          text: `${symbol} 性能指标`,
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                if (chartType === 'returns' || chartType === 'drawdowns') {
                  label += context.parsed.y.toFixed(2) + '%';
                } else {
                  label += context.parsed.y.toFixed(2);
                }
              }
              return label;
            }
          }
        }
      },
    };
    
    if (chartType === 'returns') {
      return {
        ...baseOptions,
        scales: {
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: '日收益率 (%)',
            },
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            grid: {
              drawOnChartArea: false,
            },
            title: {
              display: true,
              text: '累积收益率 (%)',
            },
          },
        },
      };
    } else if (chartType === 'drawdowns') {
      return {
        ...baseOptions,
        scales: {
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: '回撤率 (%)',
            },
            min: 0,
          },
        },
      };
    } else if (chartType === 'sharpe') {
      return {
        ...baseOptions,
        scales: {
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: '夏普比率',
            },
          },
        },
      };
    }
  };

  const handleDaysChange = (value) => {
    setDays(value);
  };

  const handleChartTypeChange = (value) => {
    setChartType(value);
  };

  return (
    <Card 
      title="性能指标" 
      extra={
        <div style={{ display: 'flex', gap: '10px' }}>
          <Select
            defaultValue={chartType}
            style={{ width: 120 }}
            onChange={handleChartTypeChange}
          >
            <Option value="returns">收益率</Option>
            <Option value="drawdowns">回撤率</Option>
            <Option value="sharpe">夏普比率</Option>
          </Select>
          <Select
            defaultValue={days}
            style={{ width: 120 }}
            onChange={handleDaysChange}
          >
            <Option value={7}>7天</Option>
            <Option value={14}>14天</Option>
            <Option value={30}>30天</Option>
            <Option value={60}>60天</Option>
            <Option value={90}>90天</Option>
          </Select>
        </div>
      }
    >
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <Spin size="large" />
        </div>
      ) : chartData ? (
        <Line options={getOptions()} data={chartData} height={80} />
      ) : (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          无数据可显示
        </div>
      )}
    </Card>
  );
};

export default PerformanceChart;

