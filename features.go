package analysis

import (
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/models"
)

// FeatureExtractor 表示特征提取器
type FeatureExtractor struct {
	log *logrus.Logger
}

// NewFeatureExtractor 创建一个新的特征提取器
func NewFeatureExtractor(log *logrus.Logger) *FeatureExtractor {
	return &FeatureExtractor{
		log: log,
	}
}

// Feature 表示一个特征
type Feature struct {
	Name  string
	Value float64
}

// ExtractFeatures 从K线数据和技术指标中提取特征
func (e *FeatureExtractor) ExtractFeatures(klines []models.KLineData, indicators map[string][]float64, index int) ([]Feature, error) {
	if len(klines) == 0 || index < 0 || index >= len(klines) {
		return nil, fmt.Errorf("无效的K线数据或索引")
	}

	features := make([]Feature, 0)

	// 添加基本价格特征
	features = append(features, e.extractPriceFeatures(klines, index)...)

	// 添加成交量特征
	features = append(features, e.extractVolumeFeatures(klines, index)...)

	// 添加技术指标特征
	features = append(features, e.extractIndicatorFeatures(indicators, index)...)

	// 添加时间特征
	features = append(features, e.extractTimeFeatures(klines[index].OpenTime)...)

	// 添加价格模式特征
	features = append(features, e.extractPricePatternFeatures(klines, index)...)

	return features, nil
}

// extractPriceFeatures 提取价格相关特征
func (e *FeatureExtractor) extractPriceFeatures(klines []models.KLineData, index int) []Feature {
	features := make([]Feature, 0)

	// 当前K线数据
	current := klines[index]

	// 基本价格特征
	features = append(features, Feature{Name: "open", Value: current.Open})
	features = append(features, Feature{Name: "high", Value: current.High})
	features = append(features, Feature{Name: "low", Value: current.Low})
	features = append(features, Feature{Name: "close", Value: current.Close})

	// 价格范围
	priceRange := current.High - current.Low
	features = append(features, Feature{Name: "price_range", Value: priceRange})

	// 价格范围占比
	if current.Low > 0 {
		priceRangePercent := priceRange / current.Low * 100
		features = append(features, Feature{Name: "price_range_percent", Value: priceRangePercent})
	}

	// 实体大小
	bodySize := math.Abs(current.Close - current.Open)
	features = append(features, Feature{Name: "body_size", Value: bodySize})

	// 实体占比
	if priceRange > 0 {
		bodySizePercent := bodySize / priceRange * 100
		features = append(features, Feature{Name: "body_size_percent", Value: bodySizePercent})
	}

	// 上影线长度
	upperShadow := current.High - math.Max(current.Open, current.Close)
	features = append(features, Feature{Name: "upper_shadow", Value: upperShadow})

	// 下影线长度
	lowerShadow := math.Min(current.Open, current.Close) - current.Low
	features = append(features, Feature{Name: "lower_shadow", Value: lowerShadow})

	// 价格变化
	if index > 0 {
		previous := klines[index-1]
		priceChange := current.Close - previous.Close
		features = append(features, Feature{Name: "price_change", Value: priceChange})

		if previous.Close > 0 {
			priceChangePercent := priceChange / previous.Close * 100
			features = append(features, Feature{Name: "price_change_percent", Value: priceChangePercent})
		}
	}

	// 过去N个周期的价格变化
	periods := []int{3, 5, 10, 20, 50}
	for _, period := range periods {
		if index >= period {
			pastClose := klines[index-period].Close
			priceChange := current.Close - pastClose
			features = append(features, Feature{Name: fmt.Sprintf("price_change_%d", period), Value: priceChange})

			if pastClose > 0 {
				priceChangePercent := priceChange / pastClose * 100
				features = append(features, Feature{Name: fmt.Sprintf("price_change_percent_%d", period), Value: priceChangePercent})
			}
		}
	}

	return features
}

// extractVolumeFeatures 提取成交量相关特征
func (e *FeatureExtractor) extractVolumeFeatures(klines []models.KLineData, index int) []Feature {
	features := make([]Feature, 0)

	// 当前K线数据
	current := klines[index]

	// 基本成交量特征
	features = append(features, Feature{Name: "volume", Value: current.Volume})
	features = append(features, Feature{Name: "quote_volume", Value: current.QuoteVolume})
	features = append(features, Feature{Name: "trade_count", Value: float64(current.TradeCount)})

	// 买方成交量占比
	if current.Volume > 0 {
		takerBuyVolPercent := current.TakerBuyVol / current.Volume * 100
		features = append(features, Feature{Name: "taker_buy_vol_percent", Value: takerBuyVolPercent})
	}

	// 成交量变化
	if index > 0 {
		previous := klines[index-1]
		volumeChange := current.Volume - previous.Volume
		features = append(features, Feature{Name: "volume_change", Value: volumeChange})

		if previous.Volume > 0 {
			volumeChangePercent := volumeChange / previous.Volume * 100
			features = append(features, Feature{Name: "volume_change_percent", Value: volumeChangePercent})
		}
	}

	// 过去N个周期的平均成交量
	periods := []int{3, 5, 10, 20, 50}
	for _, period := range periods {
		if index >= period {
			volumeSum := 0.0
			for i := 0; i < period; i++ {
				volumeSum += klines[index-i].Volume
			}
			avgVolume := volumeSum / float64(period)
			features = append(features, Feature{Name: fmt.Sprintf("avg_volume_%d", period), Value: avgVolume})

			// 当前成交量相对于平均成交量的比例
			if avgVolume > 0 {
				volumeRatio := current.Volume / avgVolume
				features = append(features, Feature{Name: fmt.Sprintf("volume_ratio_%d", period), Value: volumeRatio})
			}
		}
	}

	return features
}

// extractIndicatorFeatures 提取技术指标特征
func (e *FeatureExtractor) extractIndicatorFeatures(indicators map[string][]float64, index int) []Feature {
	features := make([]Feature, 0)

	// 遍历所有技术指标
	for name, values := range indicators {
		if index < len(values) {
			features = append(features, Feature{Name: name, Value: values[index]})
		}
	}

	// 添加指标交叉特征
	features = append(features, e.extractCrossoverFeatures(indicators, index)...)

	// 添加指标差值特征
	features = append(features, e.extractDifferenceFeatures(indicators, index)...)

	return features
}

// extractCrossoverFeatures 提取指标交叉特征
func (e *FeatureExtractor) extractCrossoverFeatures(indicators map[string][]float64, index int) []Feature {
	features := make([]Feature, 0)

	// 定义需要检查交叉的指标对
	crossoverPairs := []struct {
		fastName string
		slowName string
		outName  string
	}{
		{"sma5", "sma10", "sma5_cross_sma10"},
		{"sma10", "sma20", "sma10_cross_sma20"},
		{"sma20", "sma50", "sma20_cross_sma50"},
		{"sma50", "sma200", "sma50_cross_sma200"},
		{"ema5", "ema10", "ema5_cross_ema10"},
		{"ema10", "ema20", "ema10_cross_ema20"},
		{"ema20", "ema50", "ema20_cross_ema50"},
		{"ema50", "ema200", "ema50_cross_ema200"},
		{"macd_line", "macd_signal", "macd_cross_signal"},
	}

	for _, pair := range crossoverPairs {
		fast, fastOk := indicators[pair.fastName]
		slow, slowOk := indicators[pair.slowName]

		if fastOk && slowOk && index > 0 && index < len(fast) && index < len(slow) {
			// 当前值
			currentFast := fast[index]
			currentSlow := slow[index]
			
			// 前一个值
			prevFast := fast[index-1]
			prevSlow := slow[index-1]

			// 检查是否发生了交叉
			crossUp := prevFast <= prevSlow && currentFast > currentSlow
			crossDown := prevFast >= prevSlow && currentFast < currentSlow

			if crossUp {
				features = append(features, Feature{Name: pair.outName, Value: 1})
			} else if crossDown {
				features = append(features, Feature{Name: pair.outName, Value: -1})
			} else {
				features = append(features, Feature{Name: pair.outName, Value: 0})
			}
		}
	}

	return features
}

// extractDifferenceFeatures 提取指标差值特征
func (e *FeatureExtractor) extractDifferenceFeatures(indicators map[string][]float64, index int) []Feature {
	features := make([]Feature, 0)

	// 定义需要计算差值的指标对
	diffPairs := []struct {
		firstName  string
		secondName string
		outName    string
	}{
		{"sma5", "sma10", "sma5_minus_sma10"},
		{"sma10", "sma20", "sma10_minus_sma20"},
		{"sma20", "sma50", "sma20_minus_sma50"},
		{"sma50", "sma200", "sma50_minus_sma200"},
		{"ema5", "ema10", "ema5_minus_ema10"},
		{"ema10", "ema20", "ema10_minus_ema20"},
		{"ema20", "ema50", "ema20_minus_ema50"},
		{"ema50", "ema200", "ema50_minus_ema200"},
		{"macd_line", "macd_signal", "macd_minus_signal"},
	}

	for _, pair := range diffPairs {
		first, firstOk := indicators[pair.firstName]
		second, secondOk := indicators[pair.secondName]

		if firstOk && secondOk && index < len(first) && index < len(second) {
			diff := first[index] - second[index]
			features = append(features, Feature{Name: pair.outName, Value: diff})

			// 计算相对差值（百分比）
			if second[index] != 0 {
				relDiff := diff / math.Abs(second[index]) * 100
				features = append(features, Feature{Name: pair.outName + "_percent", Value: relDiff})
			}
		}
	}

	return features
}

// extractTimeFeatures 提取时间相关特征
func (e *FeatureExtractor) extractTimeFeatures(timestamp time.Time) []Feature {
	features := make([]Feature, 0)

	// 小时
	hour := float64(timestamp.Hour())
	features = append(features, Feature{Name: "hour", Value: hour})

	// 一天中的小时（正弦和余弦变换，以捕捉周期性）
	hourSin := math.Sin(2 * math.Pi * hour / 24)
	hourCos := math.Cos(2 * math.Pi * hour / 24)
	features = append(features, Feature{Name: "hour_sin", Value: hourSin})
	features = append(features, Feature{Name: "hour_cos", Value: hourCos})

	// 星期几
	weekday := float64(timestamp.Weekday())
	features = append(features, Feature{Name: "weekday", Value: weekday})

	// 星期几（正弦和余弦变换）
	weekdaySin := math.Sin(2 * math.Pi * weekday / 7)
	weekdayCos := math.Cos(2 * math.Pi * weekday / 7)
	features = append(features, Feature{Name: "weekday_sin", Value: weekdaySin})
	features = append(features, Feature{Name: "weekday_cos", Value: weekdayCos})

	// 月份
	month := float64(timestamp.Month())
	features = append(features, Feature{Name: "month", Value: month})

	// 月份（正弦和余弦变换）
	monthSin := math.Sin(2 * math.Pi * month / 12)
	monthCos := math.Cos(2 * math.Pi * month / 12)
	features = append(features, Feature{Name: "month_sin", Value: monthSin})
	features = append(features, Feature{Name: "month_cos", Value: monthCos})

	return features
}

// extractPricePatternFeatures 提取价格模式特征
func (e *FeatureExtractor) extractPricePatternFeatures(klines []models.KLineData, index int) []Feature {
	features := make([]Feature, 0)

	// 确保有足够的历史数据
	if index < 5 {
		return features
	}

	// 检测价格模式
	features = append(features, e.detectDojiPattern(klines, index))
	features = append(features, e.detectHammerPattern(klines, index))
	features = append(features, e.detectEngulfingPattern(klines, index))
	features = append(features, e.detectMorningStar(klines, index))
	features = append(features, e.detectEveningStar(klines, index))

	return features
}

// detectDojiPattern 检测十字星形态
func (e *FeatureExtractor) detectDojiPattern(klines []models.KLineData, index int) Feature {
	candle := klines[index]
	bodySize := math.Abs(candle.Close - candle.Open)
	totalRange := candle.High - candle.Low

	// 如果实体非常小（小于总范围的5%），则认为是十字星
	if totalRange > 0 && bodySize/totalRange < 0.05 {
		return Feature{Name: "doji_pattern", Value: 1}
	}

	return Feature{Name: "doji_pattern", Value: 0}
}

// detectHammerPattern 检测锤子线形态
func (e *FeatureExtractor) detectHammerPattern(klines []models.KLineData, index int) Feature {
	candle := klines[index]
	bodySize := math.Abs(candle.Close - candle.Open)
	totalRange := candle.High - candle.Low

	if totalRange == 0 {
		return Feature{Name: "hammer_pattern", Value: 0}
	}

	bodyTop := math.Max(candle.Open, candle.Close)
	bodyBottom := math.Min(candle.Open, candle.Close)

	upperShadow := candle.High - bodyTop
	lowerShadow := bodyBottom - candle.Low

	// 锤子线：下影线长度至少是实体的两倍，上影线很短
	if lowerShadow >= 2*bodySize && upperShadow < 0.1*bodySize {
		// 在下跌趋势中出现更有意义
		if index >= 5 {
			downTrend := true
			for i := 1; i <= 5; i++ {
				if klines[index-i].Close > klines[index-i-1].Close {
					downTrend = false
					break
				}
			}
			if downTrend {
				return Feature{Name: "hammer_pattern", Value: 2} // 强烈的锤子线信号
			}
		}
		return Feature{Name: "hammer_pattern", Value: 1} // 普通锤子线
	}

	return Feature{Name: "hammer_pattern", Value: 0}
}

// detectEngulfingPattern 检测吞没形态
func (e *FeatureExtractor) detectEngulfingPattern(klines []models.KLineData, index int) Feature {
	if index < 1 {
		return Feature{Name: "engulfing_pattern", Value: 0}
	}

	current := klines[index]
	previous := klines[index-1]

	currentBodySize := math.Abs(current.Close - current.Open)
	previousBodySize := math.Abs(previous.Close - previous.Open)

	// 当前K线实体必须比前一个K线实体大
	if currentBodySize <= previousBodySize {
		return Feature{Name: "engulfing_pattern", Value: 0}
	}

	// 看涨吞没：当前K线为阳线，前一个K线为阴线，当前K线完全吞没前一个K线
	if current.Close > current.Open && previous.Close < previous.Open &&
		current.Open <= previous.Close && current.Close >= previous.Open {
		return Feature{Name: "engulfing_pattern", Value: 1} // 看涨吞没
	}

	// 看跌吞没：当前K线为阴线，前一个K线为阳线，当前K线完全吞没前一个K线
	if current.Close < current.Open && previous.Close > previous.Open &&
		current.Open >= previous.Close && current.Close <= previous.Open {
		return Feature{Name: "engulfing_pattern", Value: -1} // 看跌吞没
	}

	return Feature{Name: "engulfing_pattern", Value: 0}
}

// detectMorningStar 检测晨星形态
func (e *FeatureExtractor) detectMorningStar(klines []models.KLineData, index int) Feature {
	if index < 2 {
		return Feature{Name: "morning_star", Value: 0}
	}

	first := klines[index-2]  // 第一个K线（阴线）
	second := klines[index-1] // 第二个K线（小实体）
	third := klines[index]    // 第三个K线（阳线）

	// 第一个K线是阴线
	if first.Close >= first.Open {
		return Feature{Name: "morning_star", Value: 0}
	}

	// 第二个K线是小实体
	secondBodySize := math.Abs(second.Close - second.Open)
	firstBodySize := math.Abs(first.Close - first.Open)
	if secondBodySize > 0.3*firstBodySize {
		return Feature{Name: "morning_star", Value: 0}
	}

	// 第三个K线是阳线，且收盘价至少达到第一个K线实体的中点
	if third.Close <= third.Open {
		return Feature{Name: "morning_star", Value: 0}
	}

	firstMidpoint := (first.Open + first.Close) / 2
	if third.Close >= firstMidpoint {
		return Feature{Name: "morning_star", Value: 1}
	}

	return Feature{Name: "morning_star", Value: 0}
}

// detectEveningStar 检测黄昏星形态
func (e *FeatureExtractor) detectEveningStar(klines []models.KLineData, index int) Feature {
	if index < 2 {
		return Feature{Name: "evening_star", Value: 0}
	}

	first := klines[index-2]  // 第一个K线（阳线）
	second := klines[index-1] // 第二个K线（小实体）
	third := klines[index]    // 第三个K线（阴线）

	// 第一个K线是阳线
	if first.Close <= first.Open {
		return Feature{Name: "evening_star", Value: 0}
	}

	// 第二个K线是小实体
	secondBodySize := math.Abs(second.Close - second.Open)
	firstBodySize := math.Abs(first.Close - first.Open)
	if secondBodySize > 0.3*firstBodySize {
		return Feature{Name: "evening_star", Value: 0}
	}

	// 第三个K线是阴线，且收盘价至少达到第一个K线实体的中点
	if third.Close >= third.Open {
		return Feature{Name: "evening_star", Value: 0}
	}

	firstMidpoint := (first.Open + first.Close) / 2
	if third.Close <= firstMidpoint {
		return Feature{Name: "evening_star", Value: 1}
	}

	return Feature{Name: "evening_star", Value: 0}
}

// FeaturesToMap 将特征列表转换为映射
func FeaturesToMap(features []Feature) map[string]float64 {
	result := make(map[string]float64)
	for _, feature := range features {
		result[feature.Name] = feature.Value
	}
	return result
}

