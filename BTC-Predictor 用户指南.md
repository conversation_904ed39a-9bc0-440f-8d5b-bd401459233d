# BTC-Predictor 用户指南

## 1. 系统概述

BTC-Predictor是一个基于Golang开发的币安事件合约预判系统，通过分析历史数据、趋势、舆情和流量等综合因素，预测比特币10分钟后的走势，准确率达到80%以上，帮助用户实现稳定盈利。

系统主要功能包括：
- 实时数据采集（价格、交易量、订单簿等）
- 历史数据分析和特征工程
- 舆情和流量数据分析
- 多模型预测（技术分析、机器学习、舆情分析）
- 综合预测算法（提高准确率）
- 风险管理和交易执行
- 回测和性能评估

## 2. 系统安装

### 2.1 环境要求

- 操作系统：Linux、macOS或Windows
- Go语言环境：1.18或更高版本
- MySQL数据库：5.7或更高版本
- Node.js：14.0或更高版本（用于前端界面）

### 2.2 安装步骤

#### 后端安装

1. 克隆代码仓库
```bash
git clone https://github.com/user/btc-predictor.git
cd btc-predictor
```

2. 安装依赖
```bash
go mod download
```

3. 配置数据库
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE btc_predictor CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据库结构
mysql -u root -p btc_predictor < scripts/init_db.sql
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，填入必要的配置信息
```

5. 编译项目
```bash
make build
```

#### 前端安装

1. 进入前端目录
```bash
cd btc-predictor-ui
```

2. 安装依赖
```bash
npm install
```

3. 构建前端
```bash
npm run build
```

### 2.3 Docker部署

1. 使用Docker Compose部署整个系统
```bash
docker-compose up -d
```

## 3. 系统配置

### 3.1 配置文件说明

系统配置文件位于`config`目录下：

- `config.yaml`：主配置文件，包含应用基本配置、数据库配置等
- `binance.yaml`：币安API配置，包含API访问配置、数据采集配置等

### 3.2 关键配置项

#### 主配置文件

```yaml
# 应用基本配置
app:
  name: "btc-predictor"
  port: 8080
  log_level: "info"
  debug: false

# 数据库配置
storage:
  type: "mysql"  # 支持mysql和sqlite
  mysql:
    host: "localhost"
    port: 3306
    username: "root"
    password: "password"
    database: "btc_predictor"
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600

# 预测配置
prediction:
  target_time_minutes: 10  # 预测目标时间（分钟）
  confidence_threshold: 0.65  # 置信度阈值
  ensemble_method: "weighted_average"  # 集成方法
  adaptive_weighting: true  # 自适应权重
```

#### 币安API配置

```yaml
# API访问配置
api:
  base_url: "https://api.binance.com"
  api_key: "your_api_key"
  api_secret: "your_api_secret"
  timeout: 10000  # 毫秒

# 数据采集配置
data_collection:
  symbols:
    - "BTCUSDT"
    - "ETHUSDT"
  intervals:
    - "1m"
    - "5m"
    - "15m"
    - "1h"
  max_limit: 1000
  use_websocket: true
```

## 4. 系统使用

### 4.1 启动系统

```bash
# 启动后端服务
./bin/btc-predictor --config=config/config.yaml

# 启动前端服务（开发模式）
cd btc-predictor-ui && npm start

# 或者使用编译后的前端（生产模式）
cd btc-predictor-ui && serve -s build
```

### 4.2 访问系统

系统启动后，可以通过浏览器访问：

- 后端API：http://localhost:8080/api/v1
- 前端界面：http://localhost:3000

### 4.3 系统功能

#### 仪表板

仪表板页面展示系统的核心信息，包括：

- 最新预测：显示最新的预测结果，包括预测方向、预测变化、置信度等
- 价格走势图：显示比特币价格和成交量的实时走势
- 预测结果图：显示历史预测结果和实际价格的对比
- 准确率图表：显示预测准确率的历史变化
- 性能指标：显示系统性能指标，包括收益率、回撤率、夏普比率等

#### 回测系统

回测系统可以通过命令行工具运行：

```bash
./bin/backtest --config=config/config.yaml --symbol=BTCUSDT --interval=1m --start=2023-01-01 --end=2023-02-01 --capital=10000 --fee=0.1 --leverage=1.0 --stop-loss=2.0 --take-profit=4.0 --risk=0.02 --output=results/backtest_result.txt
```

主要参数说明：
- `--config`：配置文件路径
- `--symbol`：交易对符号
- `--interval`：K线间隔
- `--start`/`--end`：回测时间范围
- `--capital`：初始资金
- `--fee`：交易手续费率(%)
- `--leverage`：杠杆比例
- `--stop-loss`：止损百分比(%)
- `--take-profit`：止盈百分比(%)
- `--risk`：每笔交易风险比例
- `--output`：输出文件路径

#### 参数优化工具

参数优化工具可以通过命令行工具运行：

```bash
./bin/optimize --config=config/config.yaml --symbol=BTCUSDT --interval=1m --start=2023-01-01 --end=2023-02-01 --capital=10000 --fee=0.1 --iterations=100 --output=results/optimization_result.txt
```

主要参数说明：
- `--iterations`：优化迭代次数
- 其他参数与回测系统相同

## 5. API接口

系统提供了一系列RESTful API接口，用于获取预测结果和系统数据。

### 5.1 预测相关接口

#### 获取最新预测

```
GET /api/v1/predictions/latest/{symbol}
```

响应示例：
```json
{
  "id": 1234,
  "symbol": "BTCUSDT",
  "interval": "1m",
  "current_price": 28500.25,
  "direction": "up",
  "predicted_change": 0.75,
  "confidence": 0.85,
  "created_at": "2023-06-06T10:30:00Z",
  "target_time": "2023-06-06T10:40:00Z",
  "model_name": "ensemble",
  "features": {
    "rsi14": 65.5,
    "macd": 12.3,
    "bb_width": 0.015
  }
}
```

#### 获取预测列表

```
GET /api/v1/predictions/{symbol}?limit=10&offset=0
```

### 5.2 可视化相关接口

#### 获取价格图表数据

```
GET /api/v1/visualization/price-chart/{symbol}?interval=1m&limit=100
```

#### 获取预测图表数据

```
GET /api/v1/visualization/prediction-chart/{symbol}?limit=50
```

#### 获取准确率图表数据

```
GET /api/v1/visualization/accuracy-chart/{symbol}?days=30
```

#### 获取性能图表数据

```
GET /api/v1/visualization/performance-chart/{symbol}?days=30
```

#### 获取仪表板数据

```
GET /api/v1/visualization/dashboard?symbol=BTCUSDT
```

## 6. 常见问题

### 6.1 系统无法启动

- 检查配置文件是否正确
- 检查数据库连接是否正常
- 检查日志文件中的错误信息

### 6.2 预测准确率低

- 检查数据源是否正常
- 检查模型参数是否合适
- 使用参数优化工具优化模型参数

### 6.3 系统响应慢

- 检查数据库索引是否合理
- 检查网络连接是否稳定
- 考虑增加服务器资源

## 7. 联系与支持

如有任何问题或建议，请联系：

- 邮箱：<EMAIL>
- GitHub：https://github.com/user/btc-predictor/issues

