package models

import (
	"time"
)

// KLineData 表示K线数据
type KLineData struct {
	ID           uint      `gorm:"primaryKey" json:"-"`
	Symbol       string    `gorm:"index:idx_symbol_interval_time" json:"symbol"`
	Interval     string    `gorm:"index:idx_symbol_interval_time" json:"interval"`
	OpenTime     time.Time `gorm:"index:idx_symbol_interval_time" json:"open_time"`
	CloseTime    time.Time `json:"close_time"`
	Open         float64   `json:"open"`
	High         float64   `json:"high"`
	Low          float64   `json:"low"`
	Close        float64   `json:"close"`
	Volume       float64   `json:"volume"`
	TradeCount   int64     `json:"trade_count"`
	QuoteVolume  float64   `json:"quote_volume"`
	TakerBuyVol  float64   `json:"taker_buy_vol"`
	TakerBuyQuoteVol float64 `json:"taker_buy_quote_vol"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 返回KLineData的表名
func (KLineData) TableName() string {
	return "kline_data"
}

// TradeData 表示交易数据
type TradeData struct {
	ID            uint      `gorm:"primaryKey" json:"-"`
	Symbol        string    `gorm:"index:idx_symbol_time" json:"symbol"`
	TradeID       int64     `gorm:"uniqueIndex" json:"trade_id"`
	Price         float64   `json:"price"`
	Quantity      float64   `json:"quantity"`
	QuoteQuantity float64   `json:"quote_quantity"`
	Time          time.Time `gorm:"index:idx_symbol_time" json:"time"`
	IsBuyerMaker  bool      `json:"is_buyer_maker"`
	IsBestMatch   bool      `json:"is_best_match"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 返回TradeData的表名
func (TradeData) TableName() string {
	return "trade_data"
}

// OrderBookEntry 表示订单簿条目
type OrderBookEntry struct {
	Price    float64 `json:"price"`
	Quantity float64 `json:"quantity"`
}

// OrderBookData 表示订单簿数据
type OrderBookData struct {
	ID         uint             `gorm:"primaryKey" json:"-"`
	Symbol     string           `gorm:"index:idx_symbol_time" json:"symbol"`
	Time       time.Time        `gorm:"index:idx_symbol_time" json:"time"`
	LastUpdateID int64          `json:"last_update_id"`
	Bids       []OrderBookEntry `gorm:"-" json:"bids"`
	Asks       []OrderBookEntry `gorm:"-" json:"asks"`
	BidsJSON   string           `gorm:"type:json" json:"-"`
	AsksJSON   string           `gorm:"type:json" json:"-"`
	CreatedAt  time.Time        `json:"created_at"`
	UpdatedAt  time.Time        `json:"updated_at"`
}

// TableName 返回OrderBookData的表名
func (OrderBookData) TableName() string {
	return "order_book_data"
}

// TickerData 表示24小时价格变动统计数据
type TickerData struct {
	ID             uint      `gorm:"primaryKey" json:"-"`
	Symbol         string    `gorm:"index:idx_symbol_time" json:"symbol"`
	Time           time.Time `gorm:"index:idx_symbol_time" json:"time"`
	PriceChange    float64   `json:"price_change"`
	PriceChangePct float64   `json:"price_change_pct"`
	WeightedAvgPrice float64 `json:"weighted_avg_price"`
	PrevClosePrice float64   `json:"prev_close_price"`
	LastPrice      float64   `json:"last_price"`
	BidPrice       float64   `json:"bid_price"`
	AskPrice       float64   `json:"ask_price"`
	OpenPrice      float64   `json:"open_price"`
	HighPrice      float64   `json:"high_price"`
	LowPrice       float64   `json:"low_price"`
	Volume         float64   `json:"volume"`
	QuoteVolume    float64   `json:"quote_volume"`
	OpenTime       time.Time `json:"open_time"`
	CloseTime      time.Time `json:"close_time"`
	FirstTradeID   int64     `json:"first_trade_id"`
	LastTradeID    int64     `json:"last_trade_id"`
	TradeCount     int64     `json:"trade_count"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// TableName 返回TickerData的表名
func (TickerData) TableName() string {
	return "ticker_data"
}

// PredictionData 表示价格预测数据
type PredictionData struct {
	ID              uint      `gorm:"primaryKey" json:"-"`
	Symbol          string    `gorm:"index:idx_symbol_time" json:"symbol"`
	PredictionTime  time.Time `gorm:"index:idx_symbol_time" json:"prediction_time"`
	TargetTime      time.Time `json:"target_time"`
	Direction       string    `json:"direction"` // "up", "down", "neutral"
	PredictedChange float64   `json:"predicted_change"`
	Confidence      float64   `json:"confidence"`
	ActualPrice     float64   `json:"actual_price"`
	ActualChange    float64   `json:"actual_change"`
	IsCorrect       bool      `json:"is_correct"`
	ModelName       string    `json:"model_name"`
	Features        string    `gorm:"type:json" json:"-"`
	FeaturesMap     map[string]float64 `gorm:"-" json:"features"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 返回PredictionData的表名
func (PredictionData) TableName() string {
	return "prediction_data"
}

// TradeResult 表示交易结果
type TradeResult struct {
	ID            uint      `gorm:"primaryKey" json:"-"`
	Symbol        string    `gorm:"index:idx_symbol_time" json:"symbol"`
	EntryTime     time.Time `gorm:"index:idx_symbol_time" json:"entry_time"`
	ExitTime      time.Time `json:"exit_time"`
	EntryPrice    float64   `json:"entry_price"`
	ExitPrice     float64   `json:"exit_price"`
	Quantity      float64   `json:"quantity"`
	Direction     string    `json:"direction"` // "long", "short"
	ProfitLoss    float64   `json:"profit_loss"`
	ProfitLossPct float64   `json:"profit_loss_pct"`
	Fees          float64   `json:"fees"`
	NetProfitLoss float64   `json:"net_profit_loss"`
	PredictionID  uint      `json:"prediction_id"`
	Notes         string    `json:"notes"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 返回TradeResult的表名
func (TradeResult) TableName() string {
	return "trade_results"
}

