import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { Card, Select, Spin } from 'antd';
import { getPredictionChartData } from '../services/api';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const { Option } = Select;

const PredictionChart = ({ symbol = 'BTCUSDT' }) => {
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState(null);
  const [limit, setLimit] = useState(50);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await getPredictionChartData(symbol, limit);
        
        // 准备图表数据
        const chartData = {
          labels: data.labels,
          datasets: [
            {
              label: '实际价格',
              data: data.actual_prices,
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.5)',
              tension: 0.1,
            },
            {
              label: '预测价格',
              data: data.predicted_prices,
              borderColor: 'rgb(255, 99, 132)',
              backgroundColor: 'rgba(255, 99, 132, 0.5)',
              tension: 0.1,
            },
            {
              label: '置信度',
              data: data.confidences.map(conf => conf * 100),
              borderColor: 'rgb(153, 102, 255)',
              backgroundColor: 'rgba(153, 102, 255, 0.5)',
              tension: 0.1,
              yAxisID: 'y1',
            },
          ],
        };
        
        setChartData(chartData);
      } catch (error) {
        console.error('加载预测图表数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    
    // 设置定时刷新
    const intervalId = setInterval(() => {
      fetchData();
    }, 300000); // 每5分钟刷新一次
    
    return () => clearInterval(intervalId);
  }, [symbol, limit]);

  const options = {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    stacked: false,
    plugins: {
      title: {
        display: true,
        text: `${symbol} 预测结果`,
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              if (context.datasetIndex === 2) {
                label += context.parsed.y.toFixed(2) + '%';
              } else {
                label += new Intl.NumberFormat('en-US', { 
                  style: 'currency', 
                  currency: 'USD',
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }).format(context.parsed.y);
              }
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: '价格 (USD)',
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        min: 0,
        max: 100,
        grid: {
          drawOnChartArea: false,
        },
        title: {
          display: true,
          text: '置信度 (%)',
        },
      },
    },
  };

  const handleLimitChange = (value) => {
    setLimit(value);
  };

  return (
    <Card 
      title="预测结果图" 
      extra={
        <Select
          defaultValue={limit}
          style={{ width: 120 }}
          onChange={handleLimitChange}
        >
          <Option value={10}>10条</Option>
          <Option value={20}>20条</Option>
          <Option value={50}>50条</Option>
          <Option value={100}>100条</Option>
        </Select>
      }
    >
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <Spin size="large" />
        </div>
      ) : chartData ? (
        <Line options={options} data={chartData} height={80} />
      ) : (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          无数据可显示
        </div>
      )}
    </Card>
  );
};

export default PredictionChart;

