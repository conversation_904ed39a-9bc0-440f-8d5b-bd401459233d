# BTC-Predictor 环境变量示例文件
# 复制此文件为.env并填入实际值

# 应用配置
APP_ENV=development
APP_LOG_LEVEL=info

# 服务器配置
SERVER_PORT=8080

# 数据库配置
DB_DRIVER=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=btcpredictor
DB_PASSWORD=password
DB_NAME=btcpredictor

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 币安API配置
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
BINANCE_USE_TESTNET=true

# 社交媒体API配置
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_SECRET=your_twitter_access_secret

REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USERNAME=your_reddit_username
REDDIT_PASSWORD=your_reddit_password

# 流量分析API配置
SIMILARWEB_API_KEY=your_similarweb_api_key
GOOGLETRENDS_API_KEY=your_googletrends_api_key

