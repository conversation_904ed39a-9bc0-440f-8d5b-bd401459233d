# BTC-Predictor Makefile

# 变量
BINARY_NAME=btc-predictor
BINARY_DIR=bin
BUILD_DIR=build
MAIN_FILE=cmd/main.go
GO=go
GOFLAGS=-v
LDFLAGS=-ldflags "-s -w"

# 默认目标
.PHONY: all
all: clean build

# 清理
.PHONY: clean
clean:
	@echo "清理..."
	@rm -rf $(BINARY_DIR)
	@rm -rf $(BUILD_DIR)
	@mkdir -p $(BINARY_DIR)
	@mkdir -p $(BUILD_DIR)

# 构建
.PHONY: build
build:
	@echo "构建..."
	@$(GO) build $(GOFLAGS) $(LDFLAGS) -o $(BINARY_DIR)/$(BINARY_NAME) $(MAIN_FILE)

# 运行
.PHONY: run
run:
	@echo "运行..."
	@$(BINARY_DIR)/$(BINARY_NAME) $(ARGS)

# 测试
.PHONY: test
test:
	@echo "测试..."
	@$(GO) test -v ./...

# 初始化数据库
.PHONY: init-db
init-db:
	@echo "初始化数据库..."
	@mysql -u root -p < scripts/init_db.sql

# 收集数据
.PHONY: collect
collect:
	@echo "收集数据..."
	@$(BINARY_DIR)/$(BINARY_NAME) -mode collect $(ARGS)

# 预测
.PHONY: predict
predict:
	@echo "预测..."
	@$(BINARY_DIR)/$(BINARY_NAME) -mode predict $(ARGS)

# 回测
.PHONY: backtest
backtest:
	@echo "回测..."
	@$(BINARY_DIR)/$(BINARY_NAME) -mode backtest $(ARGS)

# 交易
.PHONY: trade
trade:
	@echo "交易..."
	@$(BINARY_DIR)/$(BINARY_NAME) -mode trade $(ARGS)

# 帮助
.PHONY: help
help:
	@echo "BTC-Predictor Makefile"
	@echo ""
	@echo "用法:"
	@echo "  make [target] [ARGS=\"-arg1 -arg2\"]"
	@echo ""
	@echo "目标:"
	@echo "  all        - 清理并构建"
	@echo "  clean      - 清理构建目录"
	@echo "  build      - 构建项目"
	@echo "  run        - 运行项目"
	@echo "  test       - 运行测试"
	@echo "  init-db    - 初始化数据库"
	@echo "  collect    - 收集数据"
	@echo "  predict    - 运行预测"
	@echo "  backtest   - 运行回测"
	@echo "  trade      - 运行交易"
	@echo "  help       - 显示帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make collect ARGS=\"-symbol BTCUSDT -interval 1m\""
	@echo "  make predict ARGS=\"-symbol BTCUSDT -interval 10m\""
	@echo "  make backtest ARGS=\"-symbol BTCUSDT -start 2023-01-01 -end 2023-06-01\""
	@echo "  make trade ARGS=\"-symbol BTCUSDT -testnet\""

