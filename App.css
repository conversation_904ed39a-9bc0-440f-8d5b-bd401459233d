/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 布局样式 */
.site-layout-content {
  min-height: 280px;
  padding: 24px;
  background: #fff;
}

.logo {
  float: left;
  width: 120px;
  height: 31px;
  margin: 16px 24px 16px 0;
  background: rgba(255, 255, 255, 0.3);
}

.ant-row-rtl .logo {
  float: right;
  margin: 16px 0 16px 24px;
}

/* 图表容器样式 */
.chart-container {
  background: #fff;
  padding: 24px;
  margin-bottom: 24px;
  border-radius: 2px;
}

/* 卡片样式 */
.ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* 统计数字样式 */
.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ant-statistic-content {
    font-size: 16px;
  }
  
  .ant-card-head-title {
    font-size: 14px;
  }
}

/* 自定义颜色 */
.color-up {
  color: #52c41a;
}

.color-down {
  color: #f5222d;
}

.color-neutral {
  color: #1890ff;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 预测标签样式 */
.prediction-tag {
  margin-right: 8px;
  font-weight: bold;
}

