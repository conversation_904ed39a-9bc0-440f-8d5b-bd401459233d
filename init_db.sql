-- BTC-Predictor 数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS btcpredictor CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE btcpredictor;

-- K线数据表
CREATE TABLE IF NOT EXISTS kline_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    `interval` VARCHAR(10) NOT NULL,
    open_time DATETIME NOT NULL,
    close_time DATETIME NOT NULL,
    open DECIMAL(20, 8) NOT NULL,
    high DECIMAL(20, 8) NOT NULL,
    low DECIMAL(20, 8) NOT NULL,
    close DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(30, 8) NOT NULL,
    trade_count BIGINT NOT NULL,
    quote_volume DECIMAL(30, 8) NOT NULL,
    taker_buy_vol DECIMAL(30, 8) NOT NULL,
    taker_buy_quote_vol DECIMAL(30, 8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY idx_symbol_interval_time (symbol, `interval`, open_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 交易数据表
CREATE TABLE IF NOT EXISTS trade_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    trade_id BIGINT NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    quantity DECIMAL(30, 8) NOT NULL,
    quote_quantity DECIMAL(30, 8) NOT NULL,
    time DATETIME NOT NULL,
    is_buyer_maker BOOLEAN NOT NULL,
    is_best_match BOOLEAN NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY idx_trade_id (trade_id),
    INDEX idx_symbol_time (symbol, time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 订单簿数据表
CREATE TABLE IF NOT EXISTS order_book_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    time DATETIME NOT NULL,
    last_update_id BIGINT NOT NULL,
    bids_json JSON NOT NULL,
    asks_json JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol_time (symbol, time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 行情数据表
CREATE TABLE IF NOT EXISTS ticker_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    time DATETIME NOT NULL,
    price_change DECIMAL(20, 8) NOT NULL,
    price_change_pct DECIMAL(10, 4) NOT NULL,
    weighted_avg_price DECIMAL(20, 8) NOT NULL,
    prev_close_price DECIMAL(20, 8) NOT NULL,
    last_price DECIMAL(20, 8) NOT NULL,
    bid_price DECIMAL(20, 8) NOT NULL,
    ask_price DECIMAL(20, 8) NOT NULL,
    open_price DECIMAL(20, 8) NOT NULL,
    high_price DECIMAL(20, 8) NOT NULL,
    low_price DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(30, 8) NOT NULL,
    quote_volume DECIMAL(30, 8) NOT NULL,
    open_time DATETIME NOT NULL,
    close_time DATETIME NOT NULL,
    first_trade_id BIGINT NOT NULL,
    last_trade_id BIGINT NOT NULL,
    trade_count BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol_time (symbol, time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 预测数据表
CREATE TABLE IF NOT EXISTS prediction_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    prediction_time DATETIME NOT NULL,
    target_time DATETIME NOT NULL,
    direction ENUM('up', 'down', 'neutral') NOT NULL,
    predicted_change DECIMAL(10, 4) NOT NULL,
    confidence DECIMAL(5, 4) NOT NULL,
    actual_price DECIMAL(20, 8) DEFAULT NULL,
    actual_change DECIMAL(10, 4) DEFAULT NULL,
    is_correct BOOLEAN DEFAULT NULL,
    model_name VARCHAR(50) NOT NULL,
    features JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol_time (symbol, prediction_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 交易结果表
CREATE TABLE IF NOT EXISTS trade_results (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    entry_time DATETIME NOT NULL,
    exit_time DATETIME NOT NULL,
    entry_price DECIMAL(20, 8) NOT NULL,
    exit_price DECIMAL(20, 8) NOT NULL,
    quantity DECIMAL(30, 8) NOT NULL,
    direction ENUM('long', 'short') NOT NULL,
    profit_loss DECIMAL(20, 8) NOT NULL,
    profit_loss_pct DECIMAL(10, 4) NOT NULL,
    fees DECIMAL(20, 8) NOT NULL,
    net_profit_loss DECIMAL(20, 8) NOT NULL,
    prediction_id BIGINT UNSIGNED,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol_time (symbol, entry_time),
    FOREIGN KEY (prediction_id) REFERENCES prediction_data(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 舆情数据表
CREATE TABLE IF NOT EXISTS sentiment_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    source VARCHAR(50) NOT NULL,
    timestamp DATETIME NOT NULL,
    text TEXT NOT NULL,
    sentiment_score DECIMAL(5, 4) NOT NULL,
    influence DECIMAL(10, 4) NOT NULL,
    keywords JSON NOT NULL,
    url VARCHAR(255),
    author VARCHAR(100),
    likes INT UNSIGNED DEFAULT 0,
    shares INT UNSIGNED DEFAULT 0,
    comments INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_source_time (source, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 聚合舆情数据表
CREATE TABLE IF NOT EXISTS aggregated_sentiment (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp DATETIME NOT NULL,
    overall_score DECIMAL(5, 4) NOT NULL,
    positive_count INT UNSIGNED NOT NULL,
    negative_count INT UNSIGNED NOT NULL,
    neutral_count INT UNSIGNED NOT NULL,
    total_count INT UNSIGNED NOT NULL,
    sources JSON NOT NULL,
    time_window VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol_time (symbol, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 关键词趋势表
CREATE TABLE IF NOT EXISTS keyword_trend (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    keyword VARCHAR(100) NOT NULL,
    timestamp DATETIME NOT NULL,
    frequency INT UNSIGNED NOT NULL,
    source VARCHAR(50) NOT NULL,
    time_window VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_keyword_time (keyword, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 有影响力的人物发布的内容表
CREATE TABLE IF NOT EXISTS influencer_posts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    source VARCHAR(50) NOT NULL,
    timestamp DATETIME NOT NULL,
    influencer_name VARCHAR(100) NOT NULL,
    influencer_id VARCHAR(100) NOT NULL,
    text TEXT NOT NULL,
    sentiment_score DECIMAL(5, 4) NOT NULL,
    url VARCHAR(255),
    likes INT UNSIGNED DEFAULT 0,
    shares INT UNSIGNED DEFAULT 0,
    comments INT UNSIGNED DEFAULT 0,
    keywords JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_source_time (source, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 流量数据表
CREATE TABLE IF NOT EXISTS traffic_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    target VARCHAR(100) NOT NULL,
    timestamp DATETIME NOT NULL,
    visitor_count INT UNSIGNED NOT NULL,
    page_views INT UNSIGNED NOT NULL,
    bounce_rate DECIMAL(5, 2) NOT NULL,
    avg_time_on_site DECIMAL(10, 2) NOT NULL,
    new_visitors INT UNSIGNED NOT NULL,
    return_visitors INT UNSIGNED NOT NULL,
    source VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_target_time (target, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 搜索趋势数据表
CREATE TABLE IF NOT EXISTS search_trend_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    keyword VARCHAR(100) NOT NULL,
    timestamp DATETIME NOT NULL,
    search_volume INT UNSIGNED NOT NULL,
    relative_score DECIMAL(5, 2) NOT NULL,
    region VARCHAR(50) NOT NULL,
    source VARCHAR(50) NOT NULL,
    time_window VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_keyword_time (keyword, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 引荐流量数据表
CREATE TABLE IF NOT EXISTS referral_traffic (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    target VARCHAR(100) NOT NULL,
    timestamp DATETIME NOT NULL,
    referrer_site VARCHAR(100) NOT NULL,
    visitor_count INT UNSIGNED NOT NULL,
    page_views INT UNSIGNED NOT NULL,
    source VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_target_time (target, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 流量指标数据表
CREATE TABLE IF NOT EXISTS traffic_metrics (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp DATETIME NOT NULL,
    total_traffic INT UNSIGNED NOT NULL,
    traffic_change DECIMAL(10, 2) NOT NULL,
    search_volume INT UNSIGNED NOT NULL,
    search_change DECIMAL(10, 2) NOT NULL,
    social_mentions INT UNSIGNED NOT NULL,
    mentions_change DECIMAL(10, 2) NOT NULL,
    overall_score DECIMAL(5, 2) NOT NULL,
    time_window VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol_time (symbol, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建用户
CREATE USER IF NOT EXISTS 'btcpredictor'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON btcpredictor.* TO 'btcpredictor'@'localhost';
FLUSH PRIVILEGES;

