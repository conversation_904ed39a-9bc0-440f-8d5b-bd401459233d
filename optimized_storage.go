package storage

import (
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/models"
)

// OptimizedStorage 表示优化的存储实现
type OptimizedStorage struct {
	log           *logrus.Logger
	baseStorage   Storage
	cacheEnabled  bool
	cacheTTL      time.Duration
	
	// 缓存
	klineCache    map[string]CachedKLines
	sentimentCache map[string]CachedSentiments
	trafficCache  map[string]CachedTraffic
	predictionCache map[int64]*models.Prediction
	
	// 缓存锁
	klineCacheMutex    sync.RWMutex
	sentimentCacheMutex sync.RWMutex
	trafficCacheMutex  sync.RWMutex
	predictionCacheMutex sync.RWMutex
	
	// 批处理
	batchSize     int
	batchInterval time.Duration
	klineBatch    []*models.KLineData
	sentimentBatch []*models.SentimentData
	trafficBatch  []*models.TrafficData
	predictionBatch []*models.Prediction
	
	// 批处理锁
	klineBatchMutex    sync.Mutex
	sentimentBatchMutex sync.Mutex
	trafficBatchMutex  sync.Mutex
	predictionBatchMutex sync.Mutex
	
	// 停止信号
	stopChan      chan struct{}
	wg            sync.WaitGroup
}

// CachedKLines 表示缓存的K线数据
type CachedKLines struct {
	KLines    []models.KLineData
	Timestamp time.Time
}

// CachedSentiments 表示缓存的舆情数据
type CachedSentiments struct {
	Sentiments []models.SentimentData
	Timestamp  time.Time
}

// CachedTraffic 表示缓存的流量数据
type CachedTraffic struct {
	TrafficData []models.TrafficData
	Timestamp   time.Time
}

// NewOptimizedStorage 创建一个新的优化存储
func NewOptimizedStorage(log *logrus.Logger, baseStorage Storage) *OptimizedStorage {
	storage := &OptimizedStorage{
		log:           log,
		baseStorage:   baseStorage,
		cacheEnabled:  true,
		cacheTTL:      5 * time.Minute,
		
		klineCache:    make(map[string]CachedKLines),
		sentimentCache: make(map[string]CachedSentiments),
		trafficCache:  make(map[string]CachedTraffic),
		predictionCache: make(map[int64]*models.Prediction),
		
		batchSize:     100,
		batchInterval: 10 * time.Second,
		klineBatch:    make([]*models.KLineData, 0, 100),
		sentimentBatch: make([]*models.SentimentData, 0, 100),
		trafficBatch:  make([]*models.TrafficData, 0, 100),
		predictionBatch: make([]*models.Prediction, 0, 100),
		
		stopChan:      make(chan struct{}),
	}
	
	// 启动批处理协程
	storage.startBatchProcessing()
	
	return storage
}

// startBatchProcessing 启动批处理
func (s *OptimizedStorage) startBatchProcessing() {
	s.wg.Add(4)
	
	// K线批处理
	go func() {
		defer s.wg.Done()
		ticker := time.NewTicker(s.batchInterval)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				s.flushKLineBatch()
			case <-s.stopChan:
				s.flushKLineBatch()
				return
			}
		}
	}()
	
	// 舆情批处理
	go func() {
		defer s.wg.Done()
		ticker := time.NewTicker(s.batchInterval)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				s.flushSentimentBatch()
			case <-s.stopChan:
				s.flushSentimentBatch()
				return
			}
		}
	}()
	
	// 流量批处理
	go func() {
		defer s.wg.Done()
		ticker := time.NewTicker(s.batchInterval)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				s.flushTrafficBatch()
			case <-s.stopChan:
				s.flushTrafficBatch()
				return
			}
		}
	}()
	
	// 预测批处理
	go func() {
		defer s.wg.Done()
		ticker := time.NewTicker(s.batchInterval)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				s.flushPredictionBatch()
			case <-s.stopChan:
				s.flushPredictionBatch()
				return
			}
		}
	}()
}

// Stop 停止批处理
func (s *OptimizedStorage) Stop() {
	close(s.stopChan)
	s.wg.Wait()
}

// flushKLineBatch 刷新K线批处理
func (s *OptimizedStorage) flushKLineBatch() {
	s.klineBatchMutex.Lock()
	defer s.klineBatchMutex.Unlock()
	
	if len(s.klineBatch) == 0 {
		return
	}
	
	// 批量保存K线数据
	if err := s.baseStorage.SaveKLinesBatch(s.klineBatch); err != nil {
		s.log.WithError(err).Error("批量保存K线数据失败")
	}
	
	// 清空批处理
	s.klineBatch = make([]*models.KLineData, 0, s.batchSize)
}

// flushSentimentBatch 刷新舆情批处理
func (s *OptimizedStorage) flushSentimentBatch() {
	s.sentimentBatchMutex.Lock()
	defer s.sentimentBatchMutex.Unlock()
	
	if len(s.sentimentBatch) == 0 {
		return
	}
	
	// 批量保存舆情数据
	if err := s.baseStorage.SaveSentimentDataBatch(s.sentimentBatch); err != nil {
		s.log.WithError(err).Error("批量保存舆情数据失败")
	}
	
	// 清空批处理
	s.sentimentBatch = make([]*models.SentimentData, 0, s.batchSize)
}

// flushTrafficBatch 刷新流量批处理
func (s *OptimizedStorage) flushTrafficBatch() {
	s.trafficBatchMutex.Lock()
	defer s.trafficBatchMutex.Unlock()
	
	if len(s.trafficBatch) == 0 {
		return
	}
	
	// 批量保存流量数据
	if err := s.baseStorage.SaveTrafficDataBatch(s.trafficBatch); err != nil {
		s.log.WithError(err).Error("批量保存流量数据失败")
	}
	
	// 清空批处理
	s.trafficBatch = make([]*models.TrafficData, 0, s.batchSize)
}

// flushPredictionBatch 刷新预测批处理
func (s *OptimizedStorage) flushPredictionBatch() {
	s.predictionBatchMutex.Lock()
	defer s.predictionBatchMutex.Unlock()
	
	if len(s.predictionBatch) == 0 {
		return
	}
	
	// 批量保存预测数据
	if err := s.baseStorage.SavePredictionsBatch(s.predictionBatch); err != nil {
		s.log.WithError(err).Error("批量保存预测数据失败")
	}
	
	// 清空批处理
	s.predictionBatch = make([]*models.Prediction, 0, s.batchSize)
}

// SaveKLine 保存K线数据
func (s *OptimizedStorage) SaveKLine(kline *models.KLineData) error {
	// 添加到批处理
	s.klineBatchMutex.Lock()
	s.klineBatch = append(s.klineBatch, kline)
	batchFull := len(s.klineBatch) >= s.batchSize
	s.klineBatchMutex.Unlock()
	
	// 如果批处理已满，立即刷新
	if batchFull {
		s.flushKLineBatch()
	}
	
	// 更新缓存
	if s.cacheEnabled {
		s.invalidateKLineCache(kline.Symbol, kline.Interval)
	}
	
	return nil
}

// SaveKLinesBatch 批量保存K线数据
func (s *OptimizedStorage) SaveKLinesBatch(klines []*models.KLineData) error {
	// 添加到批处理
	s.klineBatchMutex.Lock()
	s.klineBatch = append(s.klineBatch, klines...)
	batchFull := len(s.klineBatch) >= s.batchSize
	s.klineBatchMutex.Unlock()
	
	// 如果批处理已满，立即刷新
	if batchFull {
		s.flushKLineBatch()
	}
	
	// 更新缓存
	if s.cacheEnabled && len(klines) > 0 {
		s.invalidateKLineCache(klines[0].Symbol, klines[0].Interval)
	}
	
	return nil
}

// GetKLinesBySymbol 获取指定交易对的K线数据
func (s *OptimizedStorage) GetKLinesBySymbol(symbol string, interval string, limit int) ([]models.KLineData, error) {
	// 检查缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("%s_%s_%d", symbol, interval, limit)
		s.klineCacheMutex.RLock()
		cached, ok := s.klineCache[cacheKey]
		s.klineCacheMutex.RUnlock()
		
		if ok && time.Since(cached.Timestamp) < s.cacheTTL {
			return cached.KLines, nil
		}
	}
	
	// 从基础存储获取数据
	klines, err := s.baseStorage.GetKLinesBySymbol(symbol, interval, limit)
	if err != nil {
		return nil, err
	}
	
	// 更新缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("%s_%s_%d", symbol, interval, limit)
		s.klineCacheMutex.Lock()
		s.klineCache[cacheKey] = CachedKLines{
			KLines:    klines,
			Timestamp: time.Now(),
		}
		s.klineCacheMutex.Unlock()
	}
	
	return klines, nil
}

// GetRecentKLines 获取最近的K线数据
func (s *OptimizedStorage) GetRecentKLines(symbol string, interval string, limit int) ([]models.KLineData, error) {
	// 检查缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("recent_%s_%s_%d", symbol, interval, limit)
		s.klineCacheMutex.RLock()
		cached, ok := s.klineCache[cacheKey]
		s.klineCacheMutex.RUnlock()
		
		if ok && time.Since(cached.Timestamp) < s.cacheTTL {
			return cached.KLines, nil
		}
	}
	
	// 从基础存储获取数据
	klines, err := s.baseStorage.GetRecentKLines(symbol, interval, limit)
	if err != nil {
		return nil, err
	}
	
	// 更新缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("recent_%s_%s_%d", symbol, interval, limit)
		s.klineCacheMutex.Lock()
		s.klineCache[cacheKey] = CachedKLines{
			KLines:    klines,
			Timestamp: time.Now(),
		}
		s.klineCacheMutex.Unlock()
	}
	
	return klines, nil
}

// GetKLinesByTimeRange 获取指定时间范围的K线数据
func (s *OptimizedStorage) GetKLinesByTimeRange(symbol string, interval string, startTime time.Time, endTime time.Time) ([]models.KLineData, error) {
	// 检查缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("range_%s_%s_%d_%d", symbol, interval, startTime.Unix(), endTime.Unix())
		s.klineCacheMutex.RLock()
		cached, ok := s.klineCache[cacheKey]
		s.klineCacheMutex.RUnlock()
		
		if ok && time.Since(cached.Timestamp) < s.cacheTTL {
			return cached.KLines, nil
		}
	}
	
	// 从基础存储获取数据
	klines, err := s.baseStorage.GetKLinesByTimeRange(symbol, interval, startTime, endTime)
	if err != nil {
		return nil, err
	}
	
	// 更新缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("range_%s_%s_%d_%d", symbol, interval, startTime.Unix(), endTime.Unix())
		s.klineCacheMutex.Lock()
		s.klineCache[cacheKey] = CachedKLines{
			KLines:    klines,
			Timestamp: time.Now(),
		}
		s.klineCacheMutex.Unlock()
	}
	
	return klines, nil
}

// GetKLineAtTime 获取指定时间的K线数据
func (s *OptimizedStorage) GetKLineAtTime(symbol string, timestamp time.Time) (*models.KLineData, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetKLineAtTime(symbol, timestamp)
}

// SaveSentimentData 保存舆情数据
func (s *OptimizedStorage) SaveSentimentData(sentiment *models.SentimentData) error {
	// 添加到批处理
	s.sentimentBatchMutex.Lock()
	s.sentimentBatch = append(s.sentimentBatch, sentiment)
	batchFull := len(s.sentimentBatch) >= s.batchSize
	s.sentimentBatchMutex.Unlock()
	
	// 如果批处理已满，立即刷新
	if batchFull {
		s.flushSentimentBatch()
	}
	
	// 更新缓存
	if s.cacheEnabled {
		s.invalidateSentimentCache()
	}
	
	return nil
}

// SaveSentimentDataBatch 批量保存舆情数据
func (s *OptimizedStorage) SaveSentimentDataBatch(sentiments []*models.SentimentData) error {
	// 添加到批处理
	s.sentimentBatchMutex.Lock()
	s.sentimentBatch = append(s.sentimentBatch, sentiments...)
	batchFull := len(s.sentimentBatch) >= s.batchSize
	s.sentimentBatchMutex.Unlock()
	
	// 如果批处理已满，立即刷新
	if batchFull {
		s.flushSentimentBatch()
	}
	
	// 更新缓存
	if s.cacheEnabled {
		s.invalidateSentimentCache()
	}
	
	return nil
}

// GetRecentSentimentData 获取最近的舆情数据
func (s *OptimizedStorage) GetRecentSentimentData(startTime time.Time, endTime time.Time) ([]models.SentimentData, error) {
	// 检查缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("sentiment_%d_%d", startTime.Unix(), endTime.Unix())
		s.sentimentCacheMutex.RLock()
		cached, ok := s.sentimentCache[cacheKey]
		s.sentimentCacheMutex.RUnlock()
		
		if ok && time.Since(cached.Timestamp) < s.cacheTTL {
			return cached.Sentiments, nil
		}
	}
	
	// 从基础存储获取数据
	sentiments, err := s.baseStorage.GetRecentSentimentData(startTime, endTime)
	if err != nil {
		return nil, err
	}
	
	// 更新缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("sentiment_%d_%d", startTime.Unix(), endTime.Unix())
		s.sentimentCacheMutex.Lock()
		s.sentimentCache[cacheKey] = CachedSentiments{
			Sentiments: sentiments,
			Timestamp:  time.Now(),
		}
		s.sentimentCacheMutex.Unlock()
	}
	
	return sentiments, nil
}

// GetRecentInfluencerPosts 获取最近的影响者发布内容
func (s *OptimizedStorage) GetRecentInfluencerPosts(startTime time.Time, endTime time.Time) ([]models.InfluencerPost, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetRecentInfluencerPosts(startTime, endTime)
}

// GetLatestSentimentAnalysis 获取最新的舆情分析
func (s *OptimizedStorage) GetLatestSentimentAnalysis() (*models.SentimentAnalysis, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetLatestSentimentAnalysis()
}

// GetSentimentAnalysisHistory 获取舆情分析历史
func (s *OptimizedStorage) GetSentimentAnalysisHistory(startTime time.Time, endTime time.Time) ([]models.SentimentAnalysis, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetSentimentAnalysisHistory(startTime, endTime)
}

// SaveTrafficData 保存流量数据
func (s *OptimizedStorage) SaveTrafficData(traffic *models.TrafficData) error {
	// 添加到批处理
	s.trafficBatchMutex.Lock()
	s.trafficBatch = append(s.trafficBatch, traffic)
	batchFull := len(s.trafficBatch) >= s.batchSize
	s.trafficBatchMutex.Unlock()
	
	// 如果批处理已满，立即刷新
	if batchFull {
		s.flushTrafficBatch()
	}
	
	// 更新缓存
	if s.cacheEnabled {
		s.invalidateTrafficCache()
	}
	
	return nil
}

// SaveTrafficDataBatch 批量保存流量数据
func (s *OptimizedStorage) SaveTrafficDataBatch(traffic []*models.TrafficData) error {
	// 添加到批处理
	s.trafficBatchMutex.Lock()
	s.trafficBatch = append(s.trafficBatch, traffic...)
	batchFull := len(s.trafficBatch) >= s.batchSize
	s.trafficBatchMutex.Unlock()
	
	// 如果批处理已满，立即刷新
	if batchFull {
		s.flushTrafficBatch()
	}
	
	// 更新缓存
	if s.cacheEnabled {
		s.invalidateTrafficCache()
	}
	
	return nil
}

// GetRecentTrafficData 获取最近的流量数据
func (s *OptimizedStorage) GetRecentTrafficData(startTime time.Time, endTime time.Time) ([]models.TrafficData, error) {
	// 检查缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("traffic_%d_%d", startTime.Unix(), endTime.Unix())
		s.trafficCacheMutex.RLock()
		cached, ok := s.trafficCache[cacheKey]
		s.trafficCacheMutex.RUnlock()
		
		if ok && time.Since(cached.Timestamp) < s.cacheTTL {
			return cached.TrafficData, nil
		}
	}
	
	// 从基础存储获取数据
	traffic, err := s.baseStorage.GetRecentTrafficData(startTime, endTime)
	if err != nil {
		return nil, err
	}
	
	// 更新缓存
	if s.cacheEnabled {
		cacheKey := fmt.Sprintf("traffic_%d_%d", startTime.Unix(), endTime.Unix())
		s.trafficCacheMutex.Lock()
		s.trafficCache[cacheKey] = CachedTraffic{
			TrafficData: traffic,
			Timestamp:   time.Now(),
		}
		s.trafficCacheMutex.Unlock()
	}
	
	return traffic, nil
}

// GetRecentSearchTrendData 获取最近的搜索趋势数据
func (s *OptimizedStorage) GetRecentSearchTrendData(startTime time.Time, endTime time.Time) ([]models.SearchTrendData, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetRecentSearchTrendData(startTime, endTime)
}

// GetRecentReferralTraffic 获取最近的引荐流量数据
func (s *OptimizedStorage) GetRecentReferralTraffic(startTime time.Time, endTime time.Time) ([]models.ReferralTraffic, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetRecentReferralTraffic(startTime, endTime)
}

// GetLatestTrafficAnalysis 获取最新的流量分析
func (s *OptimizedStorage) GetLatestTrafficAnalysis() (*models.TrafficAnalysis, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetLatestTrafficAnalysis()
}

// GetTrafficAnalysisHistory 获取流量分析历史
func (s *OptimizedStorage) GetTrafficAnalysisHistory(startTime time.Time, endTime time.Time) ([]models.TrafficAnalysis, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetTrafficAnalysisHistory(startTime, endTime)
}

// SavePrediction 保存预测
func (s *OptimizedStorage) SavePrediction(prediction *models.Prediction) error {
	// 添加到批处理
	s.predictionBatchMutex.Lock()
	s.predictionBatch = append(s.predictionBatch, prediction)
	batchFull := len(s.predictionBatch) >= s.batchSize
	s.predictionBatchMutex.Unlock()
	
	// 如果批处理已满，立即刷新
	if batchFull {
		s.flushPredictionBatch()
	}
	
	// 更新缓存
	if s.cacheEnabled && prediction.ID > 0 {
		s.predictionCacheMutex.Lock()
		s.predictionCache[prediction.ID] = prediction
		s.predictionCacheMutex.Unlock()
	}
	
	return nil
}

// SavePredictionsBatch 批量保存预测
func (s *OptimizedStorage) SavePredictionsBatch(predictions []*models.Prediction) error {
	// 添加到批处理
	s.predictionBatchMutex.Lock()
	s.predictionBatch = append(s.predictionBatch, predictions...)
	batchFull := len(s.predictionBatch) >= s.batchSize
	s.predictionBatchMutex.Unlock()
	
	// 如果批处理已满，立即刷新
	if batchFull {
		s.flushPredictionBatch()
	}
	
	// 更新缓存
	if s.cacheEnabled {
		s.predictionCacheMutex.Lock()
		for _, prediction := range predictions {
			if prediction.ID > 0 {
				s.predictionCache[prediction.ID] = prediction
			}
		}
		s.predictionCacheMutex.Unlock()
	}
	
	return nil
}

// UpdatePrediction 更新预测
func (s *OptimizedStorage) UpdatePrediction(prediction *models.Prediction) error {
	// 直接更新基础存储
	err := s.baseStorage.UpdatePrediction(prediction)
	if err != nil {
		return err
	}
	
	// 更新缓存
	if s.cacheEnabled && prediction.ID > 0 {
		s.predictionCacheMutex.Lock()
		s.predictionCache[prediction.ID] = prediction
		s.predictionCacheMutex.Unlock()
	}
	
	return nil
}

// GetPredictionByID 根据ID获取预测
func (s *OptimizedStorage) GetPredictionByID(id int64) (*models.Prediction, error) {
	// 检查缓存
	if s.cacheEnabled {
		s.predictionCacheMutex.RLock()
		cached, ok := s.predictionCache[id]
		s.predictionCacheMutex.RUnlock()
		
		if ok {
			return cached, nil
		}
	}
	
	// 从基础存储获取数据
	prediction, err := s.baseStorage.GetPredictionByID(id)
	if err != nil {
		return nil, err
	}
	
	// 更新缓存
	if s.cacheEnabled && prediction != nil {
		s.predictionCacheMutex.Lock()
		s.predictionCache[id] = prediction
		s.predictionCacheMutex.Unlock()
	}
	
	return prediction, nil
}

// GetLatestPrediction 获取最新预测
func (s *OptimizedStorage) GetLatestPrediction(symbol string) (*models.Prediction, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetLatestPrediction(symbol)
}

// GetPredictions 获取预测列表
func (s *OptimizedStorage) GetPredictions(symbol string, limit int, offset int) ([]models.Prediction, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetPredictions(symbol, limit, offset)
}

// GetUnevaluatedPredictions 获取未评估的预测
func (s *OptimizedStorage) GetUnevaluatedPredictions(currentTime time.Time) ([]models.Prediction, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetUnevaluatedPredictions(currentTime)
}

// GetAccuracyStats 获取准确率统计
func (s *OptimizedStorage) GetAccuracyStats(symbol string, startTime time.Time, endTime time.Time) (*models.AccuracyStats, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetAccuracyStats(symbol, startTime, endTime)
}

// GetPerformanceStats 获取性能统计
func (s *OptimizedStorage) GetPerformanceStats(symbol string, startTime time.Time, endTime time.Time) (*models.PerformanceStats, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetPerformanceStats(symbol, startTime, endTime)
}

// GetIndicators 获取技术指标
func (s *OptimizedStorage) GetIndicators(symbol string, interval string, limit int) (map[string][]float64, error) {
	// 从基础存储获取数据
	return s.baseStorage.GetIndicators(symbol, interval, limit)
}

// invalidateKLineCache 使K线缓存失效
func (s *OptimizedStorage) invalidateKLineCache(symbol string, interval string) {
	s.klineCacheMutex.Lock()
	defer s.klineCacheMutex.Unlock()
	
	// 删除相关缓存
	for key := range s.klineCache {
		if key == fmt.Sprintf("%s_%s_", symbol, interval) || 
		   key == fmt.Sprintf("recent_%s_%s_", symbol, interval) || 
		   key == fmt.Sprintf("range_%s_%s_", symbol, interval) {
			delete(s.klineCache, key)
		}
	}
}

// invalidateSentimentCache 使舆情缓存失效
func (s *OptimizedStorage) invalidateSentimentCache() {
	s.sentimentCacheMutex.Lock()
	defer s.sentimentCacheMutex.Unlock()
	
	// 清空缓存
	s.sentimentCache = make(map[string]CachedSentiments)
}

// invalidateTrafficCache 使流量缓存失效
func (s *OptimizedStorage) invalidateTrafficCache() {
	s.trafficCacheMutex.Lock()
	defer s.trafficCacheMutex.Unlock()
	
	// 清空缓存
	s.trafficCache = make(map[string]CachedTraffic)
}

