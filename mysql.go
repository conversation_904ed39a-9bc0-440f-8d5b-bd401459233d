package storage

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/models"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"
)

// MySQLStorage 表示MySQL存储实现
type MySQLStorage struct {
	db  *gorm.DB
	log *logrus.Logger
}

// MySQLConfig 表示MySQL配置
type MySQLConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	Database string
	Log      *logrus.Logger
}

// NewMySQLStorage 创建一个新的MySQL存储
func NewMySQLStorage(cfg MySQLConfig) (*MySQLStorage, error) {
	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL数据库失败: %w", err)
	}

	// 自动迁移表结构
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("自动迁移表结构失败: %w", err)
	}

	return &MySQLStorage{
		db:  db,
		log: cfg.Log,
	}, nil
}

// autoMigrate 自动迁移表结构
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.KLineData{},
		&models.TradeData{},
		&models.OrderBookData{},
		&models.TickerData{},
		&models.PredictionData{},
		&models.TradeResult{},
		&models.SentimentData{},
		&models.AggregatedSentiment{},
		&models.KeywordTrend{},
		&models.InfluencerPost{},
		&models.TrafficData{},
		&models.SearchTrendData{},
		&models.ReferralTraffic{},
		&models.TrafficMetrics{},
	)
}

// SaveKLineData 保存K线数据
func (s *MySQLStorage) SaveKLineData(data []models.KLineData) error {
	if len(data) == 0 {
		return nil
	}

	// 使用事务批量插入
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 批量插入，冲突时更新
		result := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "symbol"}, {Name: "interval"}, {Name: "open_time"}},
			DoUpdates: clause.AssignmentColumns([]string{"close", "high", "low", "volume", "trade_count", "quote_volume", "taker_buy_vol", "taker_buy_quote_vol"}),
		}).Create(&data)

		if result.Error != nil {
			return result.Error
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("保存K线数据失败: %w", err)
	}

	s.log.WithFields(logrus.Fields{
		"count":  len(data),
		"symbol": data[0].Symbol,
	}).Debug("保存K线数据成功")

	return nil
}

// SaveTradeData 保存交易数据
func (s *MySQLStorage) SaveTradeData(data []models.TradeData) error {
	if len(data) == 0 {
		return nil
	}

	// 使用事务批量插入
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 批量插入，冲突时忽略
		result := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "trade_id"}},
			DoNothing: true,
		}).Create(&data)

		if result.Error != nil {
			return result.Error
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("保存交易数据失败: %w", err)
	}

	s.log.WithFields(logrus.Fields{
		"count":  len(data),
		"symbol": data[0].Symbol,
	}).Debug("保存交易数据成功")

	return nil
}

// SaveOrderBookData 保存订单簿数据
func (s *MySQLStorage) SaveOrderBookData(data models.OrderBookData) error {
	result := s.db.Create(&data)
	if result.Error != nil {
		return fmt.Errorf("保存订单簿数据失败: %w", result.Error)
	}

	s.log.WithFields(logrus.Fields{
		"symbol": data.Symbol,
		"time":   data.Time,
	}).Debug("保存订单簿数据成功")

	return nil
}

// SaveTickerData 保存行情数据
func (s *MySQLStorage) SaveTickerData(data models.TickerData) error {
	result := s.db.Create(&data)
	if result.Error != nil {
		return fmt.Errorf("保存行情数据失败: %w", result.Error)
	}

	s.log.WithFields(logrus.Fields{
		"symbol": data.Symbol,
		"time":   data.Time,
	}).Debug("保存行情数据成功")

	return nil
}

// SavePredictionData 保存预测数据
func (s *MySQLStorage) SavePredictionData(data models.PredictionData) error {
	result := s.db.Create(&data)
	if result.Error != nil {
		return fmt.Errorf("保存预测数据失败: %w", result.Error)
	}

	s.log.WithFields(logrus.Fields{
		"symbol":         data.Symbol,
		"predictionTime": data.PredictionTime,
		"targetTime":     data.TargetTime,
	}).Debug("保存预测数据成功")

	return nil
}

// SaveTradeResult 保存交易结果
func (s *MySQLStorage) SaveTradeResult(data models.TradeResult) error {
	result := s.db.Create(&data)
	if result.Error != nil {
		return fmt.Errorf("保存交易结果失败: %w", result.Error)
	}

	s.log.WithFields(logrus.Fields{
		"symbol":    data.Symbol,
		"entryTime": data.EntryTime,
		"exitTime":  data.ExitTime,
	}).Debug("保存交易结果成功")

	return nil
}

// GetKLineData 获取K线数据
func (s *MySQLStorage) GetKLineData(symbol, interval string, startTime, endTime time.Time, limit int) ([]models.KLineData, error) {
	var data []models.KLineData

	query := s.db.Where("symbol = ? AND interval = ? AND open_time >= ? AND open_time <= ?",
		symbol, interval, startTime, endTime).Order("open_time ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Find(&data)
	if result.Error != nil {
		return nil, fmt.Errorf("获取K线数据失败: %w", result.Error)
	}

	return data, nil
}

// GetLatestKLine 获取最新的K线数据
func (s *MySQLStorage) GetLatestKLine(symbol, interval string) (models.KLineData, error) {
	var data models.KLineData

	result := s.db.Where("symbol = ? AND interval = ?", symbol, interval).
		Order("open_time DESC").
		First(&data)

	if result.Error != nil {
		return models.KLineData{}, fmt.Errorf("获取最新K线数据失败: %w", result.Error)
	}

	return data, nil
}

// GetPredictions 获取预测数据
func (s *MySQLStorage) GetPredictions(symbol string, startTime, endTime time.Time, limit int) ([]models.PredictionData, error) {
	var data []models.PredictionData

	query := s.db.Where("symbol = ? AND prediction_time >= ? AND prediction_time <= ?",
		symbol, startTime, endTime).Order("prediction_time DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Find(&data)
	if result.Error != nil {
		return nil, fmt.Errorf("获取预测数据失败: %w", result.Error)
	}

	return data, nil
}

// GetTradeResults 获取交易结果
func (s *MySQLStorage) GetTradeResults(symbol string, startTime, endTime time.Time, limit int) ([]models.TradeResult, error) {
	var data []models.TradeResult

	query := s.db.Where("symbol = ? AND entry_time >= ? AND entry_time <= ?",
		symbol, startTime, endTime).Order("entry_time DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Find(&data)
	if result.Error != nil {
		return nil, fmt.Errorf("获取交易结果失败: %w", result.Error)
	}

	return data, nil
}

// Close 关闭数据库连接
func (s *MySQLStorage) Close() error {
	sqlDB, err := s.db.DB()
	if err != nil {
		return fmt.Errorf("获取SQL DB失败: %w", err)
	}

	if err := sqlDB.Close(); err != nil {
		return fmt.Errorf("关闭数据库连接失败: %w", err)
	}

	return nil
}

