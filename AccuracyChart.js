import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { Card, Select, Spin } from 'antd';
import { getAccuracyChartData } from '../services/api';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  BarElement,
} from 'chart.js';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const { Option } = Select;

const AccuracyChart = ({ symbol = 'BTCUSDT' }) => {
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState(null);
  const [days, setDays] = useState(30);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await getAccuracyChartData(symbol, days);
        
        // 准备图表数据
        const chartData = {
          labels: data.labels,
          datasets: [
            {
              type: 'line',
              label: '总体准确率',
              data: data.accuracies.map(acc => acc * 100),
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.5)',
              tension: 0.1,
              yAxisID: 'y',
            },
            {
              type: 'line',
              label: '上涨准确率',
              data: data.up_accuracies.map(acc => acc * 100),
              borderColor: 'rgb(54, 162, 235)',
              backgroundColor: 'rgba(54, 162, 235, 0.5)',
              tension: 0.1,
              yAxisID: 'y',
            },
            {
              type: 'line',
              label: '下跌准确率',
              data: data.down_accuracies.map(acc => acc * 100),
              borderColor: 'rgb(255, 99, 132)',
              backgroundColor: 'rgba(255, 99, 132, 0.5)',
              tension: 0.1,
              yAxisID: 'y',
            },
            {
              type: 'bar',
              label: '预测数量',
              data: data.prediction_counts,
              borderColor: 'rgb(153, 102, 255)',
              backgroundColor: 'rgba(153, 102, 255, 0.5)',
              yAxisID: 'y1',
            },
          ],
        };
        
        setChartData(chartData);
      } catch (error) {
        console.error('加载准确率图表数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [symbol, days]);

  const options = {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    stacked: false,
    plugins: {
      title: {
        display: true,
        text: `${symbol} 预测准确率`,
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              if (context.datasetIndex <= 2) {
                label += context.parsed.y.toFixed(2) + '%';
              } else {
                label += context.parsed.y;
              }
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        min: 0,
        max: 100,
        title: {
          display: true,
          text: '准确率 (%)',
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        min: 0,
        grid: {
          drawOnChartArea: false,
        },
        title: {
          display: true,
          text: '预测数量',
        },
      },
    },
  };

  const handleDaysChange = (value) => {
    setDays(value);
  };

  return (
    <Card 
      title="预测准确率" 
      extra={
        <Select
          defaultValue={days}
          style={{ width: 120 }}
          onChange={handleDaysChange}
        >
          <Option value={7}>7天</Option>
          <Option value={14}>14天</Option>
          <Option value={30}>30天</Option>
          <Option value={60}>60天</Option>
          <Option value={90}>90天</Option>
        </Select>
      }
    >
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <Spin size="large" />
        </div>
      ) : chartData ? (
        <Line options={options} data={chartData} height={80} />
      ) : (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          无数据可显示
        </div>
      )}
    </Card>
  );
};

export default AccuracyChart;

