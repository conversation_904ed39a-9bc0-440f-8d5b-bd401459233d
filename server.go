package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"github.com/user/btc-predictor/internal/config"
	"github.com/user/btc-predictor/internal/prediction"
	"github.com/user/btc-predictor/internal/storage"
)

// Server 表示API服务器
type Server struct {
	log       *logrus.Logger
	config    config.APIConfig
	store     storage.Storage
	predictor *prediction.Predictor
	server    *http.Server
	router    *mux.Router
}

// NewServer 创建一个新的API服务器
func NewServer(log *logrus.Logger, config config.APIConfig, store storage.Storage, predictor *prediction.Predictor) *Server {
	router := mux.NewRouter()
	
	server := &Server{
		log:       log,
		config:    config,
		store:     store,
		predictor: predictor,
		router:    router,
	}

	// 注册路由
	server.registerRoutes()

	// 创建HTTP服务器
	server.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", config.Port),
		Handler: router,
	}

	return server
}

// registerRoutes 注册API路由
func (s *Server) registerRoutes() {
	// API版本前缀
	api := s.router.PathPrefix("/api/v1").Subrouter()

	// 健康检查
	api.HandleFunc("/health", s.handleHealth).Methods("GET")

	// 预测相关
	api.HandleFunc("/predictions/latest", s.handleGetLatestPrediction).Methods("GET")
	api.HandleFunc("/predictions", s.handleGetPredictions).Methods("GET")
	api.HandleFunc("/predictions/{id}", s.handleGetPrediction).Methods("GET")

	// 市场数据相关
	api.HandleFunc("/market/klines", s.handleGetKLines).Methods("GET")
	api.HandleFunc("/market/indicators", s.handleGetIndicators).Methods("GET")

	// 舆情数据相关
	api.HandleFunc("/sentiment/latest", s.handleGetLatestSentiment).Methods("GET")
	api.HandleFunc("/sentiment/history", s.handleGetSentimentHistory).Methods("GET")

	// 流量数据相关
	api.HandleFunc("/traffic/latest", s.handleGetLatestTraffic).Methods("GET")
	api.HandleFunc("/traffic/history", s.handleGetTrafficHistory).Methods("GET")

	// 统计相关
	api.HandleFunc("/stats/accuracy", s.handleGetAccuracyStats).Methods("GET")
	api.HandleFunc("/stats/performance", s.handleGetPerformanceStats).Methods("GET")

	// 中间件
	s.router.Use(s.loggingMiddleware)
	s.router.Use(s.corsMiddleware)
}

// Start 启动API服务器
func (s *Server) Start() error {
	s.log.WithField("port", s.config.Port).Info("启动API服务器")
	return s.server.ListenAndServe()
}

// Stop 停止API服务器
func (s *Server) Stop(ctx context.Context) error {
	s.log.Info("关闭API服务器")
	return s.server.Shutdown(ctx)
}

// loggingMiddleware 日志中间件
func (s *Server) loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		next.ServeHTTP(w, r)
		s.log.WithFields(logrus.Fields{
			"method":   r.Method,
			"path":     r.URL.Path,
			"duration": time.Since(start),
			"remote":   r.RemoteAddr,
		}).Info("API请求")
	})
}

// corsMiddleware CORS中间件
func (s *Server) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// handleHealth 处理健康检查请求
func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	s.respondWithJSON(w, http.StatusOK, map[string]string{
		"status": "ok",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// handleGetLatestPrediction 处理获取最新预测请求
func (s *Server) handleGetLatestPrediction(w http.ResponseWriter, r *http.Request) {
	symbol := r.URL.Query().Get("symbol")
	if symbol == "" {
		symbol = "BTCUSDT" // 默认值
	}

	prediction, err := s.store.GetLatestPrediction(symbol)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取最新预测失败: "+err.Error())
		return
	}

	if prediction == nil {
		s.respondWithError(w, http.StatusNotFound, "未找到预测")
		return
	}

	s.respondWithJSON(w, http.StatusOK, prediction)
}

// handleGetPredictions 处理获取预测列表请求
func (s *Server) handleGetPredictions(w http.ResponseWriter, r *http.Request) {
	symbol := r.URL.Query().Get("symbol")
	if symbol == "" {
		symbol = "BTCUSDT" // 默认值
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 10 // 默认值
	if limitStr != "" {
		var err error
		limit, err = strconv.Atoi(limitStr)
		if err != nil {
			s.respondWithError(w, http.StatusBadRequest, "无效的limit参数")
			return
		}
	}

	offsetStr := r.URL.Query().Get("offset")
	offset := 0 // 默认值
	if offsetStr != "" {
		var err error
		offset, err = strconv.Atoi(offsetStr)
		if err != nil {
			s.respondWithError(w, http.StatusBadRequest, "无效的offset参数")
			return
		}
	}

	predictions, err := s.store.GetPredictions(symbol, limit, offset)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取预测列表失败: "+err.Error())
		return
	}

	s.respondWithJSON(w, http.StatusOK, predictions)
}

// handleGetPrediction 处理获取单个预测请求
func (s *Server) handleGetPrediction(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr := vars["id"]
	
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		s.respondWithError(w, http.StatusBadRequest, "无效的ID")
		return
	}

	prediction, err := s.store.GetPredictionByID(id)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取预测失败: "+err.Error())
		return
	}

	if prediction == nil {
		s.respondWithError(w, http.StatusNotFound, "未找到预测")
		return
	}

	s.respondWithJSON(w, http.StatusOK, prediction)
}

// handleGetKLines 处理获取K线数据请求
func (s *Server) handleGetKLines(w http.ResponseWriter, r *http.Request) {
	symbol := r.URL.Query().Get("symbol")
	if symbol == "" {
		symbol = "BTCUSDT" // 默认值
	}

	interval := r.URL.Query().Get("interval")
	if interval == "" {
		interval = "1m" // 默认值
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 100 // 默认值
	if limitStr != "" {
		var err error
		limit, err = strconv.Atoi(limitStr)
		if err != nil {
			s.respondWithError(w, http.StatusBadRequest, "无效的limit参数")
			return
		}
	}

	klines, err := s.store.GetRecentKLines(symbol, interval, limit)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取K线数据失败: "+err.Error())
		return
	}

	s.respondWithJSON(w, http.StatusOK, klines)
}

// handleGetIndicators 处理获取技术指标请求
func (s *Server) handleGetIndicators(w http.ResponseWriter, r *http.Request) {
	symbol := r.URL.Query().Get("symbol")
	if symbol == "" {
		symbol = "BTCUSDT" // 默认值
	}

	interval := r.URL.Query().Get("interval")
	if interval == "" {
		interval = "1m" // 默认值
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 100 // 默认值
	if limitStr != "" {
		var err error
		limit, err = strconv.Atoi(limitStr)
		if err != nil {
			s.respondWithError(w, http.StatusBadRequest, "无效的limit参数")
			return
		}
	}

	// 获取K线数据
	klines, err := s.store.GetRecentKLines(symbol, interval, limit)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取K线数据失败: "+err.Error())
		return
	}

	// 计算技术指标
	indicators, err := s.store.GetIndicators(symbol, interval, limit)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取技术指标失败: "+err.Error())
		return
	}

	// 组合结果
	result := map[string]interface{}{
		"klines":     klines,
		"indicators": indicators,
	}

	s.respondWithJSON(w, http.StatusOK, result)
}

// handleGetLatestSentiment 处理获取最新舆情数据请求
func (s *Server) handleGetLatestSentiment(w http.ResponseWriter, r *http.Request) {
	sentiment, err := s.store.GetLatestSentimentAnalysis()
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取最新舆情数据失败: "+err.Error())
		return
	}

	if sentiment == nil {
		s.respondWithError(w, http.StatusNotFound, "未找到舆情数据")
		return
	}

	s.respondWithJSON(w, http.StatusOK, sentiment)
}

// handleGetSentimentHistory 处理获取舆情历史数据请求
func (s *Server) handleGetSentimentHistory(w http.ResponseWriter, r *http.Request) {
	daysStr := r.URL.Query().Get("days")
	days := 7 // 默认值
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil {
			s.respondWithError(w, http.StatusBadRequest, "无效的days参数")
			return
		}
	}

	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)

	sentiments, err := s.store.GetSentimentAnalysisHistory(startTime, endTime)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取舆情历史数据失败: "+err.Error())
		return
	}

	s.respondWithJSON(w, http.StatusOK, sentiments)
}

// handleGetLatestTraffic 处理获取最新流量数据请求
func (s *Server) handleGetLatestTraffic(w http.ResponseWriter, r *http.Request) {
	traffic, err := s.store.GetLatestTrafficAnalysis()
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取最新流量数据失败: "+err.Error())
		return
	}

	if traffic == nil {
		s.respondWithError(w, http.StatusNotFound, "未找到流量数据")
		return
	}

	s.respondWithJSON(w, http.StatusOK, traffic)
}

// handleGetTrafficHistory 处理获取流量历史数据请求
func (s *Server) handleGetTrafficHistory(w http.ResponseWriter, r *http.Request) {
	daysStr := r.URL.Query().Get("days")
	days := 7 // 默认值
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil {
			s.respondWithError(w, http.StatusBadRequest, "无效的days参数")
			return
		}
	}

	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)

	traffic, err := s.store.GetTrafficAnalysisHistory(startTime, endTime)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取流量历史数据失败: "+err.Error())
		return
	}

	s.respondWithJSON(w, http.StatusOK, traffic)
}

// handleGetAccuracyStats 处理获取准确率统计请求
func (s *Server) handleGetAccuracyStats(w http.ResponseWriter, r *http.Request) {
	daysStr := r.URL.Query().Get("days")
	days := 30 // 默认值
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil {
			s.respondWithError(w, http.StatusBadRequest, "无效的days参数")
			return
		}
	}

	symbol := r.URL.Query().Get("symbol")
	if symbol == "" {
		symbol = "BTCUSDT" // 默认值
	}

	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)

	stats, err := s.store.GetAccuracyStats(symbol, startTime, endTime)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取准确率统计失败: "+err.Error())
		return
	}

	s.respondWithJSON(w, http.StatusOK, stats)
}

// handleGetPerformanceStats 处理获取性能统计请求
func (s *Server) handleGetPerformanceStats(w http.ResponseWriter, r *http.Request) {
	daysStr := r.URL.Query().Get("days")
	days := 30 // 默认值
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil {
			s.respondWithError(w, http.StatusBadRequest, "无效的days参数")
			return
		}
	}

	symbol := r.URL.Query().Get("symbol")
	if symbol == "" {
		symbol = "BTCUSDT" // 默认值
	}

	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)

	stats, err := s.store.GetPerformanceStats(symbol, startTime, endTime)
	if err != nil {
		s.respondWithError(w, http.StatusInternalServerError, "获取性能统计失败: "+err.Error())
		return
	}

	s.respondWithJSON(w, http.StatusOK, stats)
}

// respondWithError 返回错误响应
func (s *Server) respondWithError(w http.ResponseWriter, code int, message string) {
	s.respondWithJSON(w, code, map[string]string{"error": message})
}

// respondWithJSON 返回JSON响应
func (s *Server) respondWithJSON(w http.ResponseWriter, code int, payload interface{}) {
	response, err := json.Marshal(payload)
	if err != nil {
		s.log.WithError(err).Error("序列化JSON响应失败")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	w.Write(response)
}

