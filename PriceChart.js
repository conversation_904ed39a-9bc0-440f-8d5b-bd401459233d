import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { Card, Select, Spin } from 'antd';
import { getPriceChartData } from '../services/api';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const { Option } = Select;

const PriceChart = ({ symbol = 'BTCUSDT' }) => {
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState(null);
  const [interval, setInterval] = useState('1m');
  const [limit, setLimit] = useState(100);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await getPriceChartData(symbol, interval, limit);
        
        // 准备图表数据
        const chartData = {
          labels: data.labels,
          datasets: [
            {
              label: `${symbol} 价格`,
              data: data.prices,
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.5)',
              tension: 0.1,
              yAxisID: 'y',
            },
            {
              label: `${symbol} 成交量`,
              data: data.volumes,
              borderColor: 'rgb(153, 102, 255)',
              backgroundColor: 'rgba(153, 102, 255, 0.5)',
              tension: 0.1,
              yAxisID: 'y1',
            },
          ],
        };
        
        setChartData(chartData);
      } catch (error) {
        console.error('加载价格图表数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    
    // 设置定时刷新
    const intervalId = setInterval(() => {
      fetchData();
    }, 60000); // 每分钟刷新一次
    
    return () => clearInterval(intervalId);
  }, [symbol, interval, limit]);

  const options = {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    stacked: false,
    plugins: {
      title: {
        display: true,
        text: `${symbol} 价格和成交量`,
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              if (context.datasetIndex === 0) {
                label += new Intl.NumberFormat('en-US', { 
                  style: 'currency', 
                  currency: 'USD',
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }).format(context.parsed.y);
              } else {
                label += new Intl.NumberFormat('en-US').format(context.parsed.y);
              }
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: '价格 (USD)',
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
        title: {
          display: true,
          text: '成交量',
        },
      },
    },
  };

  const handleIntervalChange = (value) => {
    setInterval(value);
  };

  const handleLimitChange = (value) => {
    setLimit(value);
  };

  return (
    <Card 
      title="价格走势图" 
      extra={
        <div style={{ display: 'flex', gap: '10px' }}>
          <Select
            defaultValue={interval}
            style={{ width: 120 }}
            onChange={handleIntervalChange}
          >
            <Option value="1m">1分钟</Option>
            <Option value="5m">5分钟</Option>
            <Option value="15m">15分钟</Option>
            <Option value="30m">30分钟</Option>
            <Option value="1h">1小时</Option>
            <Option value="4h">4小时</Option>
            <Option value="1d">1天</Option>
          </Select>
          <Select
            defaultValue={limit}
            style={{ width: 120 }}
            onChange={handleLimitChange}
          >
            <Option value={50}>50条</Option>
            <Option value={100}>100条</Option>
            <Option value={200}>200条</Option>
            <Option value={500}>500条</Option>
          </Select>
        </div>
      }
    >
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <Spin size="large" />
        </div>
      ) : chartData ? (
        <Line options={options} data={chartData} height={80} />
      ) : (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          无数据可显示
        </div>
      )}
    </Card>
  );
};

export default PriceChart;

