import React, { useState } from 'react';
import { Layout, Menu, Row, Col, Card, Select, Typography, Divider } from 'antd';
import { 
  DashboardOutlined, 
  LineChartOutlined, 
  BarChartOutlined, 
  PieChartOutlined,
  SettingOutlined
} from '@ant-design/icons';
import PriceChart from '../components/PriceChart';
import PredictionChart from '../components/PredictionChart';
import AccuracyChart from '../components/AccuracyChart';
import PerformanceChart from '../components/PerformanceChart';
import LatestPrediction from '../components/LatestPrediction';

const { Header, Content, Sider } = Layout;
const { Option } = Select;
const { Title } = Typography;

const Dashboard = () => {
  const [symbol, setSymbol] = useState('BTCUSDT');
  const [collapsed, setCollapsed] = useState(false);

  const handleSymbolChange = (value) => {
    setSymbol(value);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ display: 'flex', alignItems: 'center', padding: '0 16px', background: '#001529' }}>
        <div style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>
          BTC-Predictor 预测系统
        </div>
        <div style={{ marginLeft: 'auto' }}>
          <Select
            defaultValue={symbol}
            style={{ width: 120 }}
            onChange={handleSymbolChange}
          >
            <Option value="BTCUSDT">BTCUSDT</Option>
            <Option value="ETHUSDT">ETHUSDT</Option>
            <Option value="BNBUSDT">BNBUSDT</Option>
          </Select>
        </div>
      </Header>
      <Layout>
        <Sider 
          collapsible 
          collapsed={collapsed} 
          onCollapse={setCollapsed}
          width={200}
        >
          <Menu
            theme="dark"
            defaultSelectedKeys={['dashboard']}
            mode="inline"
          >
            <Menu.Item key="dashboard" icon={<DashboardOutlined />}>
              仪表板
            </Menu.Item>
            <Menu.Item key="price" icon={<LineChartOutlined />}>
              价格分析
            </Menu.Item>
            <Menu.Item key="prediction" icon={<BarChartOutlined />}>
              预测分析
            </Menu.Item>
            <Menu.Item key="performance" icon={<PieChartOutlined />}>
              性能分析
            </Menu.Item>
            <Menu.Item key="settings" icon={<SettingOutlined />}>
              系统设置
            </Menu.Item>
          </Menu>
        </Sider>
        <Content style={{ margin: '16px' }}>
          <Title level={2}>比特币预测仪表板</Title>
          <Divider />
          
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={8}>
              <LatestPrediction symbol={symbol} />
            </Col>
            <Col xs={24} lg={16}>
              <PriceChart symbol={symbol} />
            </Col>
          </Row>
          
          <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col xs={24} lg={24}>
              <PredictionChart symbol={symbol} />
            </Col>
          </Row>
          
          <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col xs={24} lg={12}>
              <AccuracyChart symbol={symbol} />
            </Col>
            <Col xs={24} lg={12}>
              <PerformanceChart symbol={symbol} />
            </Col>
          </Row>
          
          <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col span={24}>
              <Card title="系统信息">
                <p><strong>系统版本：</strong> v1.0.0</p>
                <p><strong>最后更新：</strong> 2023-06-06</p>
                <p><strong>数据来源：</strong> 币安API</p>
                <p><strong>预测周期：</strong> 10分钟</p>
                <p><strong>系统状态：</strong> 正常运行中</p>
              </Card>
            </Col>
          </Row>
        </Content>
      </Layout>
    </Layout>
  );
};

export default Dashboard;

